[gd_scene load_steps=7 format=3 uid="uid://bx8vn7qkqxqxq"]

[ext_resource type="Script" uid="uid://dcflxi1ai8tpb" path="res://scripts/MainMenu.gd" id="1_main"]
[ext_resource type="Texture2D" uid="uid://bebx3tdqwiil" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/BQ.png" id="2_background"]
[ext_resource type="Texture2D" uid="uid://bsynv0a2bikii" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/Overlay Title Game.png" id="3_title_overlay"]
[ext_resource type="Texture2D" uid="uid://c04urphevw0x6" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/Button Normal.png" id="4_button_normal"]
[ext_resource type="Texture2D" uid="uid://blse0wie8krge" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/Button Hover.png" id="5_button_hover"]
[ext_resource type="FontFile" uid="uid://md6m40unc1ik" path="res://assets/UI MMORPG Dark Templar Wenrexa/Font/Berry Rotunda.ttf" id="6_font"]

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_main")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("2_background")
expand_mode = 1
stretch_mode = 6

[node name="TitleOverlay" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -400.0
offset_top = -380.0
offset_right = 400.0
offset_bottom = -180.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("3_title_overlay")
expand_mode = 1
stretch_mode = 4

[node name="TitleLabel" type="Label" parent="TitleOverlay"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -50.0
offset_right = 300.0
offset_bottom = 30.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.8)
theme_override_constants/shadow_offset_x = 3
theme_override_constants/shadow_offset_y = 3
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 48
text = "Prekliate Dedičstvo"
horizontal_alignment = 1
vertical_alignment = 1

[node name="MenuContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -50.0
offset_right = 200.0
offset_bottom = 300.0
grow_horizontal = 2
grow_vertical = 2

[node name="ButtonContainer" type="VBoxContainer" parent="MenuContainer"]
custom_minimum_size = Vector2(400, 0)
layout_mode = 2

[node name="NovaHraButton" type="TextureButton" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(400, 80)
layout_mode = 2
texture_normal = ExtResource("4_button_normal")
texture_pressed = ExtResource("4_button_normal")
texture_hover = ExtResource("5_button_hover")
stretch_mode = 0

[node name="NovaHraLabel" type="Label" parent="MenuContainer/ButtonContainer/NovaHraButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.8)
theme_override_constants/shadow_offset_x = 2
theme_override_constants/shadow_offset_y = 2
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 24
text = "Nová Hra"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer1" type="Control" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="PokracovatButton" type="TextureButton" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(400, 80)
layout_mode = 2
texture_normal = ExtResource("4_button_normal")
texture_pressed = ExtResource("4_button_normal")
texture_hover = ExtResource("5_button_hover")
stretch_mode = 0

[node name="PokracovatLabel" type="Label" parent="MenuContainer/ButtonContainer/PokracovatButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.8)
theme_override_constants/shadow_offset_x = 2
theme_override_constants/shadow_offset_y = 2
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 24
text = "Pokračovať"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer2" type="Control" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="KapitolyButton" type="TextureButton" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(400, 80)
layout_mode = 2
texture_normal = ExtResource("4_button_normal")
texture_pressed = ExtResource("4_button_normal")
texture_hover = ExtResource("5_button_hover")
stretch_mode = 0

[node name="KapitolyLabel" type="Label" parent="MenuContainer/ButtonContainer/KapitolyButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.8)
theme_override_constants/shadow_offset_x = 2
theme_override_constants/shadow_offset_y = 2
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 24
text = "Kapitoly"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer3" type="Control" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="NastaveniaButton" type="TextureButton" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(400, 80)
layout_mode = 2
texture_normal = ExtResource("4_button_normal")
texture_pressed = ExtResource("4_button_normal")
texture_hover = ExtResource("5_button_hover")
stretch_mode = 0

[node name="NastaveniaLabel" type="Label" parent="MenuContainer/ButtonContainer/NastaveniaButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.8)
theme_override_constants/shadow_offset_x = 2
theme_override_constants/shadow_offset_y = 2
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 24
text = "Nastavenia"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer4" type="Control" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="AtmosphereTestButton" type="TextureButton" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(400, 80)
layout_mode = 2
texture_normal = ExtResource("4_button_normal")
texture_pressed = ExtResource("4_button_normal")
texture_hover = ExtResource("5_button_hover")
stretch_mode = 0

[node name="AtmosphereTestLabel" type="Label" parent="MenuContainer/ButtonContainer/AtmosphereTestButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.8)
theme_override_constants/shadow_offset_x = 2
theme_override_constants/shadow_offset_y = 2
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 20
text = "🌪️ Test Atmosféry"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer5" type="Control" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="OHreButton" type="TextureButton" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(400, 80)
layout_mode = 2
texture_normal = ExtResource("4_button_normal")
texture_pressed = ExtResource("4_button_normal")
texture_hover = ExtResource("5_button_hover")
stretch_mode = 0

[node name="OHreLabel" type="Label" parent="MenuContainer/ButtonContainer/OHreButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.8)
theme_override_constants/shadow_offset_x = 2
theme_override_constants/shadow_offset_y = 2
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 24
text = "O Hre"
horizontal_alignment = 1
vertical_alignment = 1

[node name="VersionLabel" type="Label" parent="."]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -120.0
offset_top = -40.0
offset_right = -20.0
offset_bottom = -10.0
grow_horizontal = 0
grow_vertical = 0
theme_override_colors/font_color = Color(0.6, 0.6, 0.6, 0.8)
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 14
text = "verzia 1.0.0"
horizontal_alignment = 2
vertical_alignment = 2
