extends Control
class_name VampireArithmeticPuzzle

signal puzzle_solved
signal puzzle_failed

@onready var puzzle_panel: NinePatchRect = $PuzzlePanel
@onready var title_label: Label = $PuzzlePanel/VBoxContainer/TitleLabel
@onready var description_label: RichTextLabel = $PuzzlePanel/VBoxContainer/DescriptionLabel
@onready var equations_label: RichTextLabel = $PuzzlePanel/VBoxContainer/EquationsLabel
@onready var question_label: Label = $PuzzlePanel/VBoxContainer/QuestionLabel
@onready var answer_field: LineEdit = $PuzzlePanel/VBoxContainer/AnswerContainer/AnswerField
@onready var submit_button: Button = $PuzzlePanel/VBoxContainer/AnswerContainer/SubmitButton
@onready var hint_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/HintButton
@onready var close_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton

var virtual_keyboard: VirtualKeyboard

var correct_answer: int = 26  # 🦇(8) + 🩸(11) + ⚰(7) = 26
var hint_level: int = 0
var max_hints: int = 3

func _ready():
	hide()

	# Vytvorenie virtuálnej klávesnice
	create_virtual_keyboard()

	# Pripojenie signálov
	if submit_button:
		submit_button.pressed.connect(_on_submit_pressed)

	if answer_field:
		answer_field.text_submitted.connect(_on_answer_submitted)
		answer_field.focus_entered.connect(_on_input_focus_entered)
		answer_field.focus_exited.connect(_on_input_focus_exited)

	if hint_button:
		hint_button.pressed.connect(_on_hint_pressed)
	if close_button:
		close_button.pressed.connect(_on_close_pressed)

func show_puzzle():
	show()
	reset_puzzle()
	# Uložiť stav puzzle
	GameManager.set_game_state_puzzle("VampireArithmeticPuzzle")
	if answer_field:
		answer_field.grab_focus()
	# Zobraziť virtuálnu klávesnicu
	if virtual_keyboard:
		virtual_keyboard.show_keyboard(answer_field)

func reset_puzzle():
	hint_level = 0
	if answer_field:
		answer_field.text = ""
		answer_field.placeholder_text = "Zadajte 2-ciferné číslo..."

func _on_submit_pressed():
	check_answer()

func _on_answer_submitted(text: String):
	check_answer()

func check_answer():
	if not answer_field:
		return
	
	var player_answer = answer_field.text.strip_edges()
	
	if player_answer.is_empty():
		show_feedback("Zadajte odpoveď!", false)
		return
	
	if not player_answer.is_valid_int():
		show_feedback("Zadajte platné číslo!", false)
		return
	
	var answer_int = player_answer.to_int()
	
	if answer_int == correct_answer:
		AudioManager.play_puzzle_success_sound()
		show_feedback("Výborne! Elixír je pripravený!", true)
		await get_tree().create_timer(1.5).timeout
		puzzle_solved.emit()
		hide()
	else:
		AudioManager.play_puzzle_error_sound()
		show_feedback("Nesprávne. Skúste znova.", false)
		if answer_field:
			answer_field.text = ""

func show_feedback(message: String, success: bool):
	if answer_field:
		if success:
			answer_field.modulate = Color.GREEN
		else:
			answer_field.modulate = Color.RED
		
		# Zobraz správu v placeholder
		answer_field.placeholder_text = message
		
		var tween = create_tween()
		tween.tween_property(answer_field, "modulate", Color.WHITE, 1.0)

func _on_hint_pressed():
	hint_level += 1
	
	if hint_level <= max_hints:
		var hint_text = get_hint_text(hint_level)
		show_hint_dialog(hint_text)
	else:
		show_hint_dialog("Už ste použili všetky nápovedy!")

func get_hint_text(level: int) -> String:
	match level:
		1:
			return "Každý symbol skrýva číslo. Začnite s netopierom."
		2:
			return "Ak dva netopiere dávajú 16, koľko je jeden netopier?"
		3:
			return "Netopier=8, Krv=11, Rakva=7. Spočítajte ich."
		_:
			return "Už ste použili všetky nápovedy!"

func show_hint_dialog(hint_text: String):
	# Vytvorenie vlastného hint panelu
	var hint_overlay = ColorRect.new()
	hint_overlay.color = Color(0, 0, 0, 0.8)
	hint_overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	add_child(hint_overlay)

	var hint_panel = NinePatchRect.new()
	hint_panel.texture = load("res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Big_Panel.png")
	hint_panel.patch_margin_left = 25
	hint_panel.patch_margin_top = 25
	hint_panel.patch_margin_right = 25
	hint_panel.patch_margin_bottom = 25
	hint_panel.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
	hint_panel.size = Vector2(400, 200)
	hint_overlay.add_child(hint_panel)

	var vbox = VBoxContainer.new()
	vbox.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	vbox.add_theme_constant_override("separation", 15)
	hint_panel.add_child(vbox)

	# Margin pre obsah
	var margin = MarginContainer.new()
	margin.add_theme_constant_override("margin_left", 20)
	margin.add_theme_constant_override("margin_right", 20)
	margin.add_theme_constant_override("margin_top", 20)
	margin.add_theme_constant_override("margin_bottom", 20)
	vbox.add_child(margin)

	var content_vbox = VBoxContainer.new()
	content_vbox.add_theme_constant_override("separation", 15)
	margin.add_child(content_vbox)

	# Titulok
	var title_label = Label.new()
	title_label.text = "💡 Nápoveda"
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.add_theme_font_size_override("font_size", 20)
	title_label.add_theme_color_override("font_color", Color(0.9, 0.8, 0.6, 1))
	content_vbox.add_child(title_label)

	# Text nápovedy
	var hint_label = RichTextLabel.new()
	hint_label.bbcode_enabled = true
	hint_label.text = "[center]" + hint_text + "[/center]"
	hint_label.fit_content = true
	hint_label.size_flags_vertical = Control.SIZE_EXPAND_FILL
	content_vbox.add_child(hint_label)

	# Tlačidlo OK
	var ok_button = Button.new()
	ok_button.text = "OK"
	ok_button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
	ok_button.custom_minimum_size = Vector2(100, 40)
	content_vbox.add_child(ok_button)

	# Pripojenie signálu na zatvorenie
	ok_button.pressed.connect(func(): hint_overlay.queue_free())

	# Možnosť zatvoriť kliknutím na overlay
	hint_overlay.gui_input.connect(func(event):
		if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
			hint_overlay.queue_free()
	)

func create_virtual_keyboard():
	"""Vytvorí a pripojí virtuálnu klávesnicu"""
	print("🔤 VampireArithmeticPuzzle: Vytváram virtuálnu klávesnicu...")

	var keyboard_scene = preload("res://scenes/VirtualKeyboard.tscn")
	virtual_keyboard = keyboard_scene.instantiate()
	add_child(virtual_keyboard)

	# Pripojenie signálov klávesnice
	virtual_keyboard.key_pressed.connect(_on_virtual_key_pressed)
	virtual_keyboard.backspace_pressed.connect(_on_virtual_backspace_pressed)
	virtual_keyboard.space_pressed.connect(_on_virtual_space_pressed)
	virtual_keyboard.enter_pressed.connect(_on_virtual_enter_pressed)
	virtual_keyboard.submit_pressed.connect(_on_submit_pressed)
	virtual_keyboard.hint_pressed.connect(_on_hint_pressed)
	virtual_keyboard.close_pressed.connect(_on_close_pressed)

	print("🔤 VampireArithmeticPuzzle: Virtuálna klávesnica vytvorená a pripojená")

func _on_virtual_key_pressed(letter: String):
	"""Spracuje stlačenie písmena na virtuálnej klávesnici"""
	if answer_field:
		answer_field.text += letter

func _on_virtual_backspace_pressed():
	"""Spracuje stlačenie backspace na virtuálnej klávesnici"""
	if answer_field and answer_field.text.length() > 0:
		answer_field.text = answer_field.text.substr(0, answer_field.text.length() - 1)

func _on_virtual_space_pressed():
	"""Spracuje stlačenie medzery na virtuálnej klávesnici"""
	if answer_field:
		answer_field.text += " "

func _on_virtual_enter_pressed():
	"""Spracuje stlačenie enter na virtuálnej klávesnici"""
	check_answer()

func _on_input_focus_entered():
	"""Zobrazí virtuálnu klávesnicu keď sa aktivuje textové pole"""
	if virtual_keyboard:
		virtual_keyboard.show_keyboard(answer_field)

func _on_input_focus_exited():
	"""Skryje virtuálnu klávesnicu keď sa deaktivuje textové pole"""
	# Neskrývame klávesnicu automaticky, nech zostane zobrazená

func _on_close_pressed():
	# Skryť virtuálnu klávesnicu
	if virtual_keyboard:
		virtual_keyboard.hide_keyboard()

	# Obnoviť story stav
	GameManager.set_game_state_story()
	puzzle_failed.emit()
	hide()

func _input(event):
	if visible and event.is_action_pressed("ui_cancel"):
		_on_close_pressed()
