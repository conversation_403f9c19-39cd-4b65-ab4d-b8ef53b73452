extends Control
class_name MemoryTestPuzzle

signal puzzle_solved
signal puzzle_failed

@onready var puzzle_panel: NinePatchRect = $PuzzlePanel
@onready var title_label: Label = $PuzzlePanel/VBoxContainer/TitleLabel
@onready var description_label: RichTextLabel = $PuzzlePanel/VBoxContainer/DescriptionLabel
@onready var status_label: Label = $PuzzlePanel/VBoxContainer/StatusLabel
@onready var red_button: TextureButton = $PuzzlePanel/VBoxContainer/ColorContainer/RedButton
@onready var blue_button: TextureButton = $PuzzlePanel/VBoxContainer/ColorContainer/BlueButton
@onready var green_button: TextureButton = $PuzzlePanel/VBoxContainer/ColorContainer/GreenButton
@onready var hint_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/HintButton
@onready var reset_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/ResetButton
@onready var close_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton

var correct_sequence: Array[String] = []
var player_sequence: Array[String] = []
var is_showing_sequence: bool = false
var sequence_index: int = 0
var hint_level: int = 0
var max_hints: int = 3

func _ready():
	hide()

	# Nastavenie počiatočných farieb tlačidiel
	if red_button:
		red_button.modulate = Color(0.7, 0.7, 0.7, 1.0)
	if blue_button:
		blue_button.modulate = Color(0.7, 0.7, 0.7, 1.0)
	if green_button:
		green_button.modulate = Color(0.7, 0.7, 0.7, 1.0)

	# Pripojenie signálov
	if red_button:
		red_button.pressed.connect(_on_red_pressed)
	if blue_button:
		blue_button.pressed.connect(_on_blue_pressed)
	if green_button:
		green_button.pressed.connect(_on_green_pressed)
	if hint_button:
		hint_button.pressed.connect(_on_hint_pressed)
	if reset_button:
		reset_button.pressed.connect(_on_reset_pressed)
	if close_button:
		close_button.pressed.connect(_on_close_pressed)

func show_puzzle():
	show()
	generate_sequence()
	reset_puzzle()
	show_sequence()

func generate_sequence():
	correct_sequence.clear()
	var colors = ["red", "blue", "green"]
	
	# Generuj náhodnú sekvenciu 3 farieb
	for i in range(3):
		correct_sequence.append(colors[randi() % colors.size()])
	
	print("Generovaná sekvencia: ", correct_sequence)

func reset_puzzle():
	player_sequence.clear()
	sequence_index = 0
	is_showing_sequence = false
	enable_buttons(false)
	update_status("Pozorujte sekvenciu...")

func show_sequence():
	is_showing_sequence = true
	enable_buttons(false)
	sequence_index = 0
	update_status("Pozorujte sekvenciu...")
	
	# Spusti animáciu sekvencie
	show_next_color()

func show_next_color():
	if sequence_index >= correct_sequence.size():
		# Sekvencia skončila
		is_showing_sequence = false
		enable_buttons(true)
		update_status("Teraz zopakujte sekvenciu...")
		return
	
	var color = correct_sequence[sequence_index]
	var button = get_color_button(color)
	
	if button:
		# Zvýrazni tlačidlo
		button.modulate = Color.WHITE
		await get_tree().create_timer(0.8).timeout
		
		# Vráť normálnu farbu
		button.modulate = Color(0.7, 0.7, 0.7, 1.0)
		await get_tree().create_timer(0.3).timeout
	
	sequence_index += 1
	show_next_color()

func get_color_button(color: String) -> TextureButton:
	match color:
		"red":
			return red_button
		"blue":
			return blue_button
		"green":
			return green_button
		_:
			return null

func enable_buttons(enabled: bool):
	if red_button:
		red_button.disabled = not enabled
	if blue_button:
		blue_button.disabled = not enabled
	if green_button:
		green_button.disabled = not enabled

func update_status(text: String):
	if status_label:
		status_label.text = text

func _on_red_pressed():
	if not is_showing_sequence:
		add_to_sequence("red")

func _on_blue_pressed():
	if not is_showing_sequence:
		add_to_sequence("blue")

func _on_green_pressed():
	if not is_showing_sequence:
		add_to_sequence("green")

func add_to_sequence(color: String):
	player_sequence.append(color)
	
	# Skontroluj, či je farba správna
	var current_index = player_sequence.size() - 1
	if player_sequence[current_index] != correct_sequence[current_index]:
		# Nesprávna farba
		AudioManager.play_puzzle_error_sound()
		show_error_feedback()
		update_status("Nesprávne! Skúste znova.")
		await get_tree().create_timer(1.5).timeout
		reset_puzzle()
		show_sequence()
		return

	# Skontroluj, či je sekvencia kompletná
	if player_sequence.size() >= correct_sequence.size():
		# Úspech!
		AudioManager.play_puzzle_success_sound()
		update_status("Výborne! Sekvencia je správna!")
		enable_buttons(false)
		await get_tree().create_timer(1.0).timeout
		puzzle_solved.emit()
		hide()
	else:
		update_status("Pokračujte... (" + str(player_sequence.size()) + "/" + str(correct_sequence.size()) + ")")

func show_error_feedback():
	# Červené zablikanie všetkých tlačidiel
	var buttons = [red_button, blue_button, green_button]
	for button in buttons:
		if button:
			button.modulate = Color.RED
	
	var tween = create_tween()
	for button in buttons:
		if button:
			tween.parallel().tween_property(button, "modulate", Color(0.7, 0.7, 0.7, 1.0), 0.5)

func _on_hint_pressed():
	print("🔍 MemoryTestPuzzle: Hint button pressed!")
	hint_level += 1

	if hint_level <= max_hints:
		var hint_text = get_hint_text(hint_level)
		show_simple_hint_dialog(hint_text)
	else:
		show_simple_hint_dialog("Už ste použili všetky nápovedy!")

func get_hint_text(level: int) -> String:
	match level:
		1:
			return "Sústreďte sa a zapamätajte si poradie farieb."
		2:
			return "Bola to sekvencia troch farieb."
		3:
			return "Skúste znova a dávajte lepší pozor."
		_:
			return "Už ste použili všetky nápovedy!"

func show_simple_hint_dialog(hint_text: String):
	# Vytvorenie responzívneho hint dialógu
	var hint_overlay = ColorRect.new()
	hint_overlay.color = Color(0, 0, 0, 0.8)
	hint_overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	add_child(hint_overlay)

	var hint_panel = NinePatchRect.new()
	hint_panel.texture = load("res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Big_Panel.png")
	hint_panel.patch_margin_left = 25
	hint_panel.patch_margin_top = 25
	hint_panel.patch_margin_right = 25
	hint_panel.patch_margin_bottom = 25
	hint_panel.set_anchors_and_offsets_preset(Control.PRESET_CENTER)

	# Responzívna veľkosť - max 80% šírky obrazovky, min 300px
	var screen_size = get_viewport().get_visible_rect().size
	var panel_width = min(max(300, screen_size.x * 0.8), 500)
	var panel_height = min(max(150, screen_size.y * 0.4), 300)
	hint_panel.size = Vector2(panel_width, panel_height)
	hint_overlay.add_child(hint_panel)

	# VBoxContainer pre obsah
	var content_vbox = VBoxContainer.new()
	content_vbox.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	content_vbox.add_theme_constant_override("separation", 15)
	hint_panel.add_child(content_vbox)

	# Margin container
	var margin_container = MarginContainer.new()
	margin_container.add_theme_constant_override("margin_left", 20)
	margin_container.add_theme_constant_override("margin_right", 20)
	margin_container.add_theme_constant_override("margin_top", 20)
	margin_container.add_theme_constant_override("margin_bottom", 20)
	content_vbox.add_child(margin_container)

	var inner_vbox = VBoxContainer.new()
	inner_vbox.add_theme_constant_override("separation", 10)
	margin_container.add_child(inner_vbox)

	# Titulok
	var title_label = Label.new()
	title_label.text = "💡 Nápoveda"
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.add_theme_font_size_override("font_size", 20)
	title_label.add_theme_color_override("font_color", Color(0.9, 0.8, 0.6, 1))
	inner_vbox.add_child(title_label)

	# Text nápovedy s automatickým zalamovaním
	var hint_label = RichTextLabel.new()
	hint_label.bbcode_enabled = true
	hint_label.text = "[center]" + hint_text + "[/center]"
	hint_label.fit_content = true
	hint_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	hint_label.size_flags_vertical = Control.SIZE_EXPAND_FILL
	hint_label.custom_minimum_size = Vector2(0, 60)
	inner_vbox.add_child(hint_label)

	# Tlačidlo OK
	var ok_button = Button.new()
	ok_button.text = "OK"
	ok_button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
	ok_button.custom_minimum_size = Vector2(100, 40)
	inner_vbox.add_child(ok_button)

	# Pripojenie signálu na zatvorenie
	ok_button.pressed.connect(func(): hint_overlay.queue_free())

	# Možnosť zatvoriť kliknutím na overlay
	hint_overlay.gui_input.connect(func(event):
		if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
			hint_overlay.queue_free()
	)

func _on_reset_pressed():
	reset_puzzle()
	show_sequence()

func _on_close_pressed():
	puzzle_failed.emit()
	hide()

func _input(event):
	if visible and event.is_action_pressed("ui_cancel"):
		_on_close_pressed()
