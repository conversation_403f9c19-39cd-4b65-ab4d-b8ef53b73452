extends Control
class_name VirtualKeyboard

signal key_pressed(letter: String)
signal backspace_pressed
signal space_pressed
signal enter_pressed
signal submit_pressed
signal hint_pressed
signal close_pressed

@onready var keys_container: Control = $KeyboardFrame/KeysContainer
@onready var row1: HBoxContainer = $KeyboardFrame/KeysContainer/Row1
@onready var row2: HBoxContainer = $KeyboardFrame/KeysContainer/Row2
@onready var row3: HBoxContainer = $KeyboardFrame/KeysContainer/Row3
@onready var row4: HBoxContainer = $KeyboardFrame/KeysContainer/Row4
@onready var row5: HBoxContainer = $KeyboardFrame/KeysContainer/Row5

# Kompaktné QWERTY rozloženie s podporou slovenských znakov
var keyboard_layout = [
	["Q", "W", "E", "R", "T", "Y", "U", "I", "O", "P"],
	["A", "S", "D", "F", "G", "H", "J", "K", "L"],
	["Z", "X", "C", "V", "B", "N", "M", "Ž", "Š", "Č"],
	["SPACE", "BACKSPACE", "ENTER"],
	["SUBMIT", "HINT", "CLOSE"]
]

var key_texture_normal: Texture2D
var key_texture_hover: Texture2D
var target_input_field: LineEdit

func _ready():
	print("🎹 VirtualKeyboard: Inicializácia...")

	# Načítať textúry klávesov
	key_texture_normal = preload("res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Buttons/Button Normal.png")
	key_texture_hover = preload("res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Buttons/Button Hover.png")

	# Vytvoriť klávesy
	create_keyboard()

	# Skryť klávesnicu na začiatku
	hide()

	print("🎹 VirtualKeyboard: Inicializácia dokončená")

func create_keyboard():
	"""Vytvorí všetky klávesy podľa QWERTY rozloženia"""
	var rows = [row1, row2, row3, row4, row5]
	
	for row_index in range(keyboard_layout.size()):
		var row_container = rows[row_index]
		var row_keys = keyboard_layout[row_index]
		
		for key_text in row_keys:
			var key_button = create_key_button(key_text)
			row_container.add_child(key_button)

			# Pridať medzery medzi klávesmi - menšie pre kompaktnosť
			if key_text != row_keys[-1]:  # Nie je posledná klávesa v rade
				var spacer = Control.new()
				spacer.custom_minimum_size = Vector2(3, 0)
				row_container.add_child(spacer)

func create_key_button(key_text: String) -> TextureButton:
	"""Vytvorí tlačidlo pre jednu klávesu s Dark Templar štýlom"""
	var button = TextureButton.new()

	# Nastaviť textúry
	button.texture_normal = key_texture_normal
	button.texture_hover = key_texture_hover
	button.texture_pressed = key_texture_hover
	button.ignore_texture_size = true
	button.stretch_mode = TextureButton.STRETCH_SCALE

	# Nastaviť veľkosť podľa typu klávesy - lepšie proporcie
	if key_text == "SPACE":
		button.custom_minimum_size = Vector2(180, 32)
	elif key_text == "BACKSPACE":
		button.custom_minimum_size = Vector2(70, 32)
	elif key_text == "ENTER":
		button.custom_minimum_size = Vector2(70, 32)
	elif key_text == "SUBMIT":
		button.custom_minimum_size = Vector2(150, 40)
	elif key_text == "HINT":
		button.custom_minimum_size = Vector2(150, 40)
	elif key_text == "CLOSE":
		button.custom_minimum_size = Vector2(150, 40)
	else:
		button.custom_minimum_size = Vector2(32, 32)

	# Vytvoriť label pre písmeno
	var label = Label.new()
	label.text = get_display_text(key_text)
	label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	label.anchors_preset = Control.PRESET_FULL_RECT

	# Aplikovať Dark Templar font štýl
	FontLoader.apply_ui_font(label, 14)

	# Dark Templar farby a efekty
	label.add_theme_color_override("font_color", Color(0.9, 0.8, 0.6, 1))  # Zlatá farba
	label.add_theme_color_override("font_shadow_color", Color(0.2, 0.1, 0.05, 1))
	label.add_theme_constant_override("shadow_offset_x", 1)
	label.add_theme_constant_override("shadow_offset_y", 1)
	label.add_theme_color_override("font_outline_color", Color(0.2, 0.1, 0.05, 1))
	label.add_theme_constant_override("outline_size", 1)

	button.add_child(label)

	# Pripojiť signály pre vizuálne efekty
	button.pressed.connect(_on_key_pressed.bind(key_text))
	button.button_down.connect(_on_key_down.bind(button))
	button.button_up.connect(_on_key_up.bind(button))

	return button

func get_display_text(key_text: String) -> String:
	"""Vráti text na zobrazenie na klávese"""
	match key_text:
		"SPACE":
			return "MEDZ"
		"BACKSPACE":
			return "←"
		"ENTER":
			return "↵"
		"SUBMIT":
			return "POTVRDIŤ"
		"HINT":
			return "NÁPOVEDA"
		"CLOSE":
			return "ZAVRIEŤ"
		_:
			return key_text

func _on_key_pressed(key_text: String):
	"""Spracuje stlačenie klávesy - len emituje signály, nepridáva text priamo"""
	# Prehrať zvuk stlačenia klávesy
	play_key_sound()

	match key_text:
		"SPACE":
			space_pressed.emit()
		"BACKSPACE":
			backspace_pressed.emit()
		"ENTER":
			enter_pressed.emit()
		"SUBMIT":
			submit_pressed.emit()
		"HINT":
			hint_pressed.emit()
		"CLOSE":
			close_pressed.emit()
		_:
			key_pressed.emit(key_text)

func play_key_sound():
	"""Prehrá zvuk stlačenia klávesy"""
	# Použiť existujúci AudioManager pre konzistentnosť
	if AudioManager and AudioManager.has_method("play_menu_button_sound"):
		AudioManager.play_menu_button_sound()
	elif AudioManager and AudioManager.has_method("play_ui_sound"):
		# Použiť menu sound ako zvuk klávesy
		AudioManager.play_ui_sound("res://audio/UI_SOUNDS/menu_sound.wav")
	else:
		# Fallback - vytvorenie jednoduchého zvuku
		print("⚠️ AudioManager nedostupný pre zvuk klávesy")

func show_keyboard(input_field: LineEdit = null):
	"""Zobrazí klávesnicu a nastaví cieľové textové pole (len pre referenčné účely)"""
	print("🎹 VirtualKeyboard: Zobrazujem klávesnicu")
	target_input_field = input_field
	show()

	# Zabráni zobrazeniu systémovej klávesnice
	if input_field:
		input_field.virtual_keyboard_enabled = false
		print("🎹 VirtualKeyboard: Pripojená k input field: ", input_field.name)

func hide_keyboard():
	"""Skryje klávesnicu"""
	target_input_field = null
	hide()

func set_target_input_field(input_field: LineEdit):
	"""Nastaví cieľové textové pole"""
	target_input_field = input_field
	if input_field:
		input_field.virtual_keyboard_enabled = false

func _on_key_down(button: TextureButton):
	"""Vizuálny efekt pri stlačení klávesy"""
	var tween = create_tween()
	tween.tween_property(button, "modulate", Color(0.8, 0.8, 0.8), 0.1)

func _on_key_up(button: TextureButton):
	"""Vizuálny efekt pri uvoľnení klávesy"""
	var tween = create_tween()
	tween.tween_property(button, "modulate", Color.WHITE, 0.1)
