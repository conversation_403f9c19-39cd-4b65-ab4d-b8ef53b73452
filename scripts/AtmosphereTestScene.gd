extends Control

func _ready():
	# <PERSON>rip<PERSON><PERSON><PERSON> sign<PERSON><PERSON> pre vš<PERSON>ky tlačidlá
	
	# KAPITOLA 1 - STORM
	$VBoxContainer/StormStartButton.pressed.connect(_on_storm_start)
	$VBoxContainer/StormNarratorButton.pressed.connect(_on_storm_narrator)
	$VBoxContainer/StormChaosButton.pressed.connect(_on_storm_chaos)
	$VBoxContainer/StormForestButton.pressed.connect(_on_storm_forest)
	$VBoxContainer/StormStopButton.pressed.connect(_on_storm_stop)
	
	# KAPITOLA 2 - BRÁNA
	$VBoxContainer/GateStartButton.pressed.connect(_on_gate_start)
	$VBoxContainer/GateRavensButton.pressed.connect(_on_gate_ravens)
	$VBoxContainer/GatePuzzleButton.pressed.connect(_on_gate_puzzle)
	$VBoxContainer/GateOpeningButton.pressed.connect(_on_gate_opening)
	
	# KAPITOLA 3 - HALA
	$VBoxContainer/HallStartButton.pressed.connect(_on_hall_start)
	$VBoxContainer/HallClockButton.pressed.connect(_on_hall_clock)
	$VBoxContainer/HallLibraryButton.pressed.connect(_on_hall_library)
	$VBoxContainer/HallStopButton.pressed.connect(_on_hall_stop)
	
	# OSTATNÉ
	$VBoxContainer/StopAllButton.pressed.connect(_on_stop_all)
	$VBoxContainer/BackButton.pressed.connect(_on_back)

# KAPITOLA 1 - STORM FUNKCIE
func _on_storm_start():
	print("🌩️ Test: Spúšťam storm na 100%")
	AudioManager.start_storm_atmosphere()

func _on_storm_narrator():
	print("📖 Test: Storm rozprávač (70%)")
	AudioManager.storm_narrator_start()

func _on_storm_chaos():
	print("⚡ Test: Storm chaos (85%)")
	AudioManager.storm_chaos()

func _on_storm_forest():
	print("🌲 Test: Storm les (40%)")
	AudioManager.storm_forest()

func _on_storm_stop():
	print("🔇 Test: Zastavujem storm")
	AudioManager.storm_fade_out()

# KAPITOLA 2 - BRÁNA FUNKCIE
func _on_gate_start():
	print("🚪 Test: Spúšťam bránu (60%)")
	AudioManager.start_gate_atmosphere()

func _on_gate_ravens():
	print("🐦‍⬛ Test: Brána havrany (70%)")
	AudioManager.gate_ravens()

func _on_gate_puzzle():
	print("🧩 Test: Brána puzzle (75%)")
	AudioManager.gate_puzzle_emphasis()

func _on_gate_opening():
	print("🔓 Test: Brána otváranie (80%)")
	AudioManager.gate_opening()

# KAPITOLA 3 - HALA FUNKCIE
func _on_hall_start():
	print("🏰 Test: Spúšťam halu (70%)")
	AudioManager.start_hall_atmosphere()

func _on_hall_clock():
	print("🕰️ Test: Hala hodiny (80%)")
	AudioManager.hall_clock_emphasis()

func _on_hall_library():
	print("📚 Test: Hala knižnica (30%)")
	AudioManager.hall_library_transition()

func _on_hall_stop():
	print("🔇 Test: Zastavujem halu")
	AudioManager.hall_stop()

# OSTATNÉ FUNKCIE
func _on_stop_all():
	print("⏹️ Test: Zastavujem všetky atmosférické zvuky")
	AudioManager.stop_atmosphere(1.0)
	AudioManager.stop_music()

func _on_back():
	print("🔙 Návrat do hlavného menu")
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")
