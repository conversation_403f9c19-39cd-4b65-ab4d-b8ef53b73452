extends Control
class_name DialogueSystem

signal dialogue_finished
signal dialogue_advanced
signal background_change_requested(image_path: String)

# Import font utilities
const FontLoader = preload("res://scripts/font_loader.gd")

@onready var dialogue_panel: NinePatchRect = $DialoguePanel
@onready var speaker_label: Label = $DialoguePanel/MainContainer/HeaderContainer/SpeakerLabel
@onready var text_label: RichTextLabel = $DialoguePanel/MainContainer/TextContainer/TextLabel
@onready var continue_button: TextureButton = $DialoguePanel/MainContainer/ButtonContainer/ContinueButton
@onready var continue_label: Label = $DialoguePanel/MainContainer/ButtonContainer/ContinueButton/ContinueLabel
@onready var main_menu_button: TextureButton = $DialoguePanel/MainContainer/ButtonContainer/MainMenuButton
@onready var main_menu_label: Label = $DialoguePanel/MainContainer/ButtonContainer/MainMenuButton/MainMenuLabel
@onready var narrator_audio_player: AudioStreamPlayer = $NarratorAudioPlayer
@onready var character_portrait: TextureRect = $DialoguePanel/MainContainer/HeaderContainer/CharacterPortrait
@onready var lightning_video: VideoStreamPlayer = $LightningVideo

var current_dialogue: Array[Dictionary] = []
var current_line_index: int = 0
var is_typing: bool = false
var typing_speed: float = 0.1
var current_text_words: PackedStringArray = []
var current_word_index: int = 0

# Audio mapovanie pre kapitoly
var chapter1_narrator_audio_map: Dictionary = {}
var chapter2_audio_map: Dictionary = {}  # Pre rozprávača aj Viktora
var chapter3_audio_map: Dictionary = {}  # Pre rozprávača aj Viktora
var chapter4_audio_map: Dictionary = {}  # Pre rozprávača aj Viktora
var chapter5_audio_map: Dictionary = {}  # Pre rozprávača, Viktora a Van Helsing záznamy
var chapter6_audio_map: Dictionary = {}  # Pre rozprávača a Isabelle
var chapter7_audio_map: Dictionary = {}  # Pre rozprávača, Van Helsinga a Viktora
var current_chapter: int = 0

# Mapovanie postáv na portréty
var character_portraits: Dictionary = {
	"Rozprávač": "res://assets/Avatary/Rozpravac.png",
	"Viktor": "res://assets/Avatary/Viktor.png",
	"Kočiš": "res://assets/Avatary/Kocis.png",
	"Van Helsing": "res://assets/Avatary/VanHelsing.png",
	"Isabelle": "res://assets/Avatary/Isabelle.png"
}

# Mapovanie dialógov na hudbu podľa promptu
var dialogue_music_triggers: Dictionary = {
	# KAPITOLA 1: Storm Journey (úvod) → Forest of Shadows (les)
	"Marec 1894. Studený dážď bičuje okná kočiara kolísajúceho sa na úzkej ceste cez karpatské horstvo.": "storm_journey",
	"Srdce vám takmer zastane: 'Grófka je v krypte.'": "forest_shadows",

	# KAPITOLA 2: Castle Gates
	"Stojíte pred masívnou železnou bránou zdobenou heraldickými symbolmi.": "castle_gates",

	# KAPITOLA 3: Library of Secrets
	"Vstupujete do veľkej haly osvetlenej len plamenným mihotaním krbu.": "library_secrets",

	# KAPITOLA 4: Alchemy Lab (už implementované cez start_chapter_4)
	"Staré krídlo pôsobí ako vstup do iného sveta. Vzduch je ťažší, studenší.": "alchemy_lab",

	# KAPITOLA 5: Descent into Darkness → Ancient Crypts
	"Zostupujete po úzkom kamennom schodisku do hlbín zámku.": "descent_darkness",

	# KAPITOLA 6: Isabelle's Awakening → Final Ritual (cez final_battle)
	"V strede miestnosti stojí mramorový sarkofág.": "isabelle_awakening",

	# EPILÓG: Van Helsing Rescue
	"Keď Isabelle zmizne, jedna zo stien sa pomaly odsunie.": "van_helsing_rescue",
	"Stúpate späť chodbami zámku. Viktor vás čaká s úľavou v očiach.": "van_helsing_rescue"
}

func _ready():
	hide()

	# Bezpečné pripojenie signálov
	if continue_button and is_instance_valid(continue_button):
		continue_button.pressed.connect(_on_continue_pressed)
	if main_menu_button and is_instance_valid(main_menu_button):
		main_menu_button.pressed.connect(_on_main_menu_pressed)

	# Nastavenie štýlu dialógového panelu
	if dialogue_panel and is_instance_valid(dialogue_panel):
		dialogue_panel.modulate = Color(1, 1, 1, 0.95)

	# Inicializácia audio mapovania pre kapitoly
	initialize_chapter1_audio_mapping()
	initialize_chapter2_audio_mapping()
	initialize_chapter3_audio_mapping()
	initialize_chapter4_audio_mapping()
	initialize_chapter5_audio_mapping()
	initialize_chapter6_audio_mapping()
	initialize_chapter7_audio_mapping()

	# Kontrola dostupnosti všetkých potrebných nodov
	validate_node_references()

	# Aplikovanie fontov a farieb
	apply_dialogue_fonts()

func validate_node_references():
	"""Kontrola dostupnosti všetkých potrebných nodov"""
	var missing_nodes = []

	if not dialogue_panel or not is_instance_valid(dialogue_panel):
		missing_nodes.append("dialogue_panel")
	if not speaker_label or not is_instance_valid(speaker_label):
		missing_nodes.append("speaker_label")
	if not text_label or not is_instance_valid(text_label):
		missing_nodes.append("text_label")
	if not continue_button or not is_instance_valid(continue_button):
		missing_nodes.append("continue_button")
	if not continue_label or not is_instance_valid(continue_label):
		missing_nodes.append("continue_label")
	if not character_portrait or not is_instance_valid(character_portrait):
		missing_nodes.append("character_portrait")

	if missing_nodes.size() > 0:
		print("⚠️ DialogueSystem: Chýbajúce nody: ", missing_nodes)
	else:
		print("✅ DialogueSystem: Všetky nody sú dostupné")

func initialize_chapter1_audio_mapping():
	# Mapovanie textov rozprávača na audio súbory pre kapitolu 1
	# Rozprávač rozpráva len v úvodných dialógoch a interlude dialógoch (nie v hlavolamoch)
	chapter1_narrator_audio_map = {
		# Úvodné dialógy rozprávača (8 dialógov s audiom, posledný bez audia)
		"Marec 1894. Studený dážď bičuje okná kočiara kolísajúceho sa na úzkej ceste cez karpatské horstvo.": "res://audio/ZVUKY_Kapitola_1/rozpravac_001.mp3",
		"Búrka silnie s každou míľou, ktorou sa približujete k zámku Van Helsinga.": "res://audio/ZVUKY_Kapitola_1/rozpravac_002.mp3",
		"V kočiari sedíte už štyri hodiny, študujúc posledné poznámky svojho mentora.": "res://audio/ZVUKY_Kapitola_1/rozpravac_003.mp3",
		"Vzduch je napätý – nielen kvôli blížiacej sa búrke, ale aj preto, že cítite hrozbu niečoho omnoho nebezpečnejšieho.": "res://audio/ZVUKY_Kapitola_1/rozpravac_004.mp3",
		"Van Helsingov posledný telegram bol nezvyčajne stručný a naliehavý. To nie je jeho štýl – určite sa deje niečo vážne.": "res://audio/ZVUKY_Kapitola_1/rozpravac_005.mp3",
		"Vonku zúri živelný chaos: vietor vyje v korunách stromov ako tisíc duší v mukách.": "res://audio/ZVUKY_Kapitola_1/rozpravac_006.mp3",
		"Každý blesk na okamih osvetlí zahmlenú krajinu, akoby samotná príroda odmietala odhaliť svoje tajomstvá.": "res://audio/ZVUKY_Kapitola_1/rozpravac_007.mp3",
		"Kočiš je nervózny – už hodinu neprehovoril ani slovo.": "res://audio/ZVUKY_Kapitola_1/rozpravac_008.mp3",
		# "Van Helsingov list obsahuje zašifrovanú správu..." - BEZ AUDIA (pred hádankou)

		# Interlude dialógy rozprávača po prvom hlavolame (8 dialógov)
		"Srdce vám takmer zastane: 'Grófka je v krypte.'": "res://audio/ZVUKY_Kapitola_1/rozpravac_009.mp3",
		"Znamená to, že Van Helsing našiel, čo hľadal – Isabelle Báthoryovú, poslednú z prekliatej línie.": "res://audio/ZVUKY_Kapitola_1/rozpravac_010.mp3",
		"Prečo však nepríde za vami? Prečo vás volá na zámok? A prečo je jeho list taký ustráchaný?": "res://audio/ZVUKY_Kapitola_1/rozpravac_011.mp3",
		"Blesk, prudké zastavenie kočiara.": "res://audio/ZVUKY_Kapitola_1/rozpravac_012.mp3",

		# Kočiš dialógy (4 repliky)
		"To je všetko! Ďalej nejdem, páni!": "res://audio/Kocis/kocis_001.mp3",
		"Ani korunu by ste mi nedali, aby som šiel bližšie k tomu prekliatemu miestu!": "res://audio/Kocis/kocis_002.mp3",
		"Hovorí sa, že kto sa priblíži k zámku po západe slnka, už nikdy neuvidí úsvit.": "res://audio/Kocis/kocis_003.mp3",
		"Musíte pokračovať pešo. Nech vás Boh ochraňuje...": "res://audio/Kocis/kocis_004.mp3",

		# Pokračovanie interlude dialógov rozprávača
		"Kočiš vám hodí batožinu do blata a bez slova odcvála.": "res://audio/ZVUKY_Kapitola_1/rozpravac_013.mp3",
		"Jeho kočiar mizne v hukote búrky. Ostávate sami – uprostred karpatskej divočiny, s nocou, ktorá sa rýchlo blíži.": "res://audio/ZVUKY_Kapitola_1/rozpravac_014.mp3",
		"Vaše kroky sa zabárajú do hlbokého blata.": "res://audio/ZVUKY_Kapitola_1/rozpravac_015.mp3",
		"Les pôsobí ako živá bytosť – počuť šepot medzi konármi, praskot vetvičiek, vzdialené vytie, ktoré neznie celkom vlčie.": "res://audio/ZVUKY_Kapitola_1/rozpravac_016.mp3",
		"Hmla klesá a každý strom pripomína strážcu sledujúceho vaše pohyby.": "res://audio/ZVUKY_Kapitola_1/rozpravac_017.mp3",
		"Cesta sa rozdeľuje na štyri smery. Van Helsingove poznámky obsahujú zvláštnu básničku.": "res://audio/ZVUKY_Kapitola_1/rozpravac_018.mp3",
		"V denníku je poznámka: 'Východ, potom dvakrát na západ, nakoniec sever - tak sa dostaneš k zámku.'": "res://audio/ZVUKY_Kapitola_1/rozpravac_019.mp3",

		# Záverečné dialógy po druhom hlavolame (2 dialógy s audiom, tretí bez audia)
		"Konečne! Cez koruny stromov sa črtajú obrysy mohutných veží.": "res://audio/ZVUKY_Kapitola_1/rozpravac_020.mp3",
		"Zámok Van Helsinga sa týči pred vami ako čierna silueta proti búrlivej oblohe.": "res://audio/ZVUKY_Kapitola_1/rozpravac_021.mp3"
		# "Táto stavba z 13. storočia..." - BEZ AUDIA (posledný dialóg kapitoly)
	}

func initialize_chapter2_audio_mapping():
	# Mapovanie textov pre kapitolu 2 - rozprávač aj Viktor
	chapter2_audio_map = {
		# Úvodné dialógy rozprávača (4 dialógy + nápis na bráne)
		"Stojíte pred masívnou železnou bránou zdobenou heraldickými symbolmi.": "res://audio/Kapitola_2/rozpravac_022.mp3",
		"Každý erb rozpráva príbeh – moc, pád dynastie, vzostup i úpadok.": "res://audio/Kapitola_2/rozpravac_023.mp3",
		"Brána je zamknutá, v oknách nevidno svetlo. Zámok pôsobí opustene.": "res://audio/Kapitola_2/rozpravac_024.mp3",
		"Havrany krákajú zo strešných ríms, akoby sa vysmievali vašej situácii.": "res://audio/Kapitola_2/rozpravac_025.mp3",
		"Na bráne je krvou napísaný text: 'MOJE MENO JE SMRŤ, ALE POZADU SOM ŽIVOT'": "res://audio/Kapitola_2/rozpravac_026.mp3",

		# Interlude dialógy po prvom puzzle (3 dialógy rozprávača)
		"Mechanizmus zaškrípe a brána sa pomaly otvára.": "res://audio/Kapitola_2/rozpravac_027.mp3",
		"Vstupujete na nádvorie – dlažba je pokrytá machom a medzi kameňmi prerastá tráva.": "res://audio/Kapitola_2/rozpravac_028.mp3",
		"Nádvorie je tiché ako hrob. Ticho prerušuje len kvapkanie vody zo žľabov.": "res://audio/Kapitola_2/rozpravac_029.mp3",
		# "Kdesi buchnú dvere..." - bez audia

		# Viktor dialógy (8 dialógov - kompletné)
		"Vy... ste to vy?": "res://audio/Kapitola_2/Viktor_1.mp3",
		"Telegram odišiel pred troma dňami. Doktor vás čaká každú noc pri lampe.": "res://audio/Kapitola_2/Viktor_2.mp3",
		"Ale teraz nie je čas na spomienky.": "res://audio/Kapitola_2/Viktor_3.mp3",
		"Nie všetci, čo prichádzajú, patria medzi živých. Ak ste členovia Rádu, odpovedzte.": "res://audio/Kapitola_2/Viktor_4.mp3",
		"Tri otázky. Tri odpovede. Každá z iného sveta – kov, hviezda, bylina.": "res://audio/Kapitola_2/Viktor_5.mp3",

		# Záverečné dialógy Viktora po druhom puzzle
		"Tak predsa. Svetlo ešte úplne nezhaslo.": "res://audio/Kapitola_2/Viktor_6.mp3",
		"Poďte. A nech vás nočné tiene nespoznajú ako hostí.": "res://audio/Kapitola_2/Viktor_7.mp3"
		# Viktor_8.mp3 je použitý v kapitole 3
	}

func initialize_chapter3_audio_mapping():
	# Mapovanie textov pre kapitolu 3 - rozprávač aj Viktor
	print("🎵 Inicializujem audio mapovanie pre kapitolu 3...")
	chapter3_audio_map = {
		# Úvodné dialógy rozprávača (3 dialógy)
		"Vstupujete do veľkej haly osvetlenej len plamenným mihotaním krbu.": "res://audio/Kapitola_3/rozpravac_030.mp3",
		"Steny zdobí zbierka starodávnych zbraní – meče, kopije, kuše.": "res://audio/Kapitola_3/rozpravac_031.mp3",
		"V rohu tikajú staré hodiny; ich monotónne klikanie je jediné, čo narúša ticho.": "res://audio/Kapitola_3/rozpravac_032.mp3",

		# Viktor dialógy v úvodnej časti (4 dialógy)
		"Pán doktor odišiel včera večer krátko po západe slnka.": "res://audio/Kapitola_3/Viktor_8.wav",
		"Povedal iba: 'Viktor, idem preskúmať staré krídlo zámku. Ak sa do úsvitu nevrátim, pošli telegram Rádu.'": "res://audio/Kapitola_3/Viktor_9.wav",
		"Odvtedy o ňom niet správy.": "res://audio/Kapitola_3/Viktor_10.wav",
		"Do toho krídla nik nevošiel celé desaťročia. Je to najstaršia časť zámku.": "res://audio/Kapitola_3/Viktor_11.wav",

		# Interlude dialógy po prvom puzzle (3 Viktor + 5 rozprávač)
		"Vonkajšie múry! Samozrejme!": "res://audio/Kapitola_3/Viktor_12.wav",
		"Tam, kde sa najstaršie základy zámku spájajú s novou stavbou.": "res://audio/Kapitola_3/Viktor_13.wav",
		"Viem presne, kde to je - pri severozápadnej veži.": "res://audio/Kapitola_3/Viktor_14.wav",

		# Rozprávač dialógy v knižnici (rozpravac_033-037)
		"Vstupujete do rozľahlej knižnice. Regály siahajú až k stropu.": "res://audio/Kapitola_3/rozpravac_033.mp3",
		"Knihy sú v rôznych jazykoch – latinčina, nemčina, rumunčina, maďarčina.": "res://audio/Kapitola_3/rozpravac_034.mp3",
		"Väčšina diel sa venuje tematikám, ktoré nie sú pre slabé povahy: démonológia, vampirológia, alchýmia.": "res://audio/Kapitola_3/rozpravac_035.mp3",
		"Za tapisériou rodokmeňa odhaľujete tajnú priehradku.": "res://audio/Kapitola_3/rozpravac_036.mp3",
		"Ukrytá je v nej drobná kožená knižka – osobný denník Van Helsinga.": "res://audio/Kapitola_3/rozpravac_037.mp3",

		# Van Helsingov denník - Viktor číta (10 dialógov)
		"15. marca 1894 – Konečne!": "res://audio/Kapitola_3/Viktor_15.mp3",
		"Po štyroch rokoch neúnavného pátrania som našiel dôkaz, že grófka Isabelle Báthoryová prežila svoju údajnú smrť.": "res://audio/Kapitola_3/Viktor_16.mp3",
		"Oficiálne záznamy sú prepracovaná lož.": "res://audio/Kapitola_3/Viktor_17.mp3",
		"Našiel som ju! Skrýva sa v kryptách pod týmto zámkom.": "res://audio/Kapitola_3/Viktor_18.mp3",
		"18. marca – Pripravujem sa na zostup do katakomb.": "res://audio/Kapitola_3/Viktor_19.mp3",
		"Potrebujem: strieborné gule, svätenú vodu z Ríma, dubový kôl, kríž môjho starého otca.": "res://audio/Kapitola_3/Viktor_20.mp3",
		"19. marca – Viktor netuší, kam idem. Tak je to lepšie.": "res://audio/Kapitola_3/Viktor_21.mp3",
		"Ak sa nevrátim do úsvitu, nech privolá Rád.": "res://audio/Kapitola_3/Viktor_22.mp3",
		"Krypty! Nik tam nevkročil celé storočia.": "res://audio/Kapitola_3/Viktor_23.mp3",
		"Vchod je zapečatený. Čo to pán doktor robil?": "res://audio/Kapitola_3/Viktor_24.mp3"
	}
	print("✅ Kapitola 3 audio mapa inicializovaná s ", chapter3_audio_map.size(), " záznamami")
	print("🎵 Rozprávač 33-37 audio súbory pridané do mapovania")

func initialize_chapter4_audio_mapping():
	# Mapovanie textov pre kapitolu 4 - rozprávač a Viktor
	print("🎵 Inicializujem audio mapovanie pre kapitolu 4...")
	chapter4_audio_map = {
		# Úvodné dialógy rozprávača (3 dialógy)
		"Staré krídlo pôsobí ako vstup do iného sveta. Vzduch je ťažší, studenší.": "res://audio/Kapitola_4/rozpravac_038.mp3",
		"Steny pokrýva vlhkosť a medzi kameňmi rastú čudné plesne.": "res://audio/Kapitola_4/rozpravac_039.mp3",
		"Občas počuť mechanické cvakanie – v múroch stále pracujú skryté mechanizmy.": "res://audio/Kapitola_4/rozpravac_040.mp3",

		# Viktor úvodné dialógy (2 dialógy)
		"Počkajte! Spomínam si na básničku.": "res://audio/Kapitola_4/Viktor_25.mp3",
		"'Kráčaj, kde Mesiac svieti, nie tam, kde Slnko horí.' V núdzi ju vraj použijem.": "res://audio/Kapitola_4/Viktor_26.mp3",

		# Interlude dialógy po prvom puzzle - rozprávač (4 dialógy)
		"Úspech! Prešli ste bez spustenia mechanizmov.": "res://audio/Kapitola_4/rozpravac_041.mp3",
		"Na konci chodby stoja masívne dubové dvere.": "res://audio/Kapitola_4/rozpravac_042.mp3",
		"Laboratórium vyzerá ako miesto, kde sa zázraky miešajú s hrôzami.": "res://audio/Kapitola_4/rozpravac_043.mp3",
		"Stoly sú obsypané sklenenými nádobami, kotlíkmi a čudesnými prístrojmi.": "res://audio/Kapitola_4/rozpravac_044.mp3",

		# Interlude dialógy po prvom puzzle - Viktor (3 dialógy)
		"Toto je recept na ochranný elixír!": "res://audio/Kapitola_4/Viktor_27.mp3",
		"Doktor mi ho ukázal pred mesiacom.": "res://audio/Kapitola_4/Viktor_28.mp3",
		"Povedal: 'Viktor, ak ma pôjdeš hľadať do katakomb, priprav si tento elixír.'": "res://audio/Kapitola_4/Viktor_29.mp3",

		# Záverečné dialógy po druhom puzzle - rozprávač (1 dialóg)
		"Elixír v fľaštičke jemne zažiari striebristým svetlom.": "res://audio/Kapitola_4/rozpravac_045.mp3",

		# Záverečné dialógy po druhom puzzle - Viktor (2 dialógy)
		"Výborne! Teraz sa môžeme odvážiť do katakomb.": "res://audio/Kapitola_4/Viktor_30.mp3",
		"Nezabudnite – elixír chráni len hodinu.": "res://audio/Kapitola_4/Viktor_31.mp3"
	}
	print("✅ Kapitola 4 audio mapa inicializovaná s ", chapter4_audio_map.size(), " záznamami")

func initialize_chapter5_audio_mapping():
	# Mapovanie textov pre kapitolu 5 - rozprávač, Viktor a Van Helsing záznamy
	chapter5_audio_map = {
		# Úvodné dialógy rozprávača (3 dialógy)
		"Za laboratóriom sa skrýva úzke kamenné schodisko vedúce do hlbín.": "res://audio/Kapitola_5/rozpravac_046.mp3",
		"Kamene sú ošúchané tisíckami krokov, no roky tu nik nebol.": "res://audio/Kapitola_5/rozpravac_047.mp3",
		"Z hĺbky sála chlad a vlhkosť, pripomínajúce dych hrobky.": "res://audio/Kapitola_5/rozpravac_048.mp3",

		# Viktor dialógy v úvodnej časti (3 dialógy)
		"Počkám tu a budem strážiť ústup!": "res://audio/Kapitola_5/Viktor_32.mp3",
		"Ak sa niečo stane, kričte. A vezmite si toto...": "res://audio/Kapitola_5/Viktor_33.mp3",
		"Kríž požehnal sám ostrihomský arcibiskup. Môže vám zachrániť život.": "res://audio/Kapitola_5/Viktor_34.mp3",

		# Interlude dialógy po prvom puzzle - rozprávač (4 dialógy)
		"Schádzate do oválnej sály s klenbou pokrytou prastarými symbolmi.": "res://audio/Kapitola_5/rozpravac_049.mp3",
		"V strede stojí kamenný podstavec. Rozhadzané ležia doktorove veci.": "res://audio/Kapitola_5/rozpravac_050.mp3",
		"Rozbitá lampa, prázdny strieborný revolver, kožený notes s roztrhanými stranami.": "res://audio/Kapitola_5/rozpravac_051.mp3",
		"Na kameňoch tmavnú škvrny, čo nápadne pripomínajú krv. No doktora niet.": "res://audio/Kapitola_5/rozpravac_052.mp3",

		# Van Helsing záznamy (4 dialógy)
		"Je tu! Isabelle má prisluhovačov – nie upírov, ale niečo horšie.": "res://audio/Kapitola_5/Van_Helsing_Zapis_001.mp3",
		"Ako vedela, že prídem? Musím sa dostať k jej sarkofágu...": "res://audio/Kapitola_5/Van_Helsing_Zapis_002.mp3",
		"Voda dochádza, striebro tiež... sú ich príliš veľa...": "res://audio/Kapitola_5/Van_Helsing_Zapis_003.mp3",
		"Ak toto niekto nájde, dokončite, čo som začal...": "res://audio/Kapitola_5/Van_Helsing_Zapis_004.mp3",

		# Záverečné dialógy rozprávača po Van Helsing záznamoch (2 dialógy)
		"Za podstavcom odkrývate tajné dvere vedúce do rozľahlej sály.": "res://audio/Kapitola_5/rozpravac_053.mp3",
		"Uprostred stojí mohutný sarkofág zdobený tromi pákami.": "res://audio/Kapitola_5/rozpravac_054.mp3"
	}

func initialize_chapter6_audio_mapping():
	# Mapovanie textov pre kapitolu 6 - rozprávač a Isabelle
	print("🎵 Inicializujem audio mapovanie pre kapitolu 6...")
	chapter6_audio_map = {
		# Úvodné dialógy rozprávača (3 dialógy)
		"Keď sa posledná pečať uvoľní, celý sarkofág sa otrasie.": "res://audio/Kapitola_6/rozpravac_055.mp3",
		"Kameň praská pod tlakom ohromnej sily zvnútra.": "res://audio/Kapitola_6/rozpravac_056.mp3",
		"Vzduch naplní pach smrti. Z trhlín sa valí biela para.": "res://audio/Kapitola_6/rozpravac_057.mp3",

		# Isabelle úvodné dialógy (4 dialógy)
		"Konečne... po troch storočiach je moja väznica otvorená.": "res://audio/Kapitola_6/Isabelle_001.mp3",
		"Starý hlupák Van Helsing poslúžil ako dokonalá návnada.": "res://audio/Kapitola_6/Isabelle_002.mp3",
		"A vy ste jeho dielo dokonali lepšie, než som dúfala.": "res://audio/Kapitola_6/Isabelle_003.mp3",
		"Ste z Rádu Striebornej ruže, však? Vidím vo vašich očiach strach i odhodlanie.": "res://audio/Kapitola_6/Isabelle_004.mp3",

		# Interlude dialógy po prvom puzzle - Isabelle (2 dialógy)
		"Bystré! No múdrosť vás neochráni pred mojou silou.": "res://audio/Kapitola_6/Isabelle_005.mp3",
		"Tri storočia som sa živila snami o pomste.": "res://audio/Kapitola_6/Isabelle_006.mp3",

		# Interlude dialógy po prvom puzzle - rozprávač (3 dialógy)
		"Isabelle vystúpi zo sarkofágu. Už nie je tou kráskou z kroník.": "res://audio/Kapitola_6/rozpravac_058.mp3",
		"Stáročia magického spánku ju premenili na niečo horšie než smrť.": "res://audio/Kapitola_6/rozpravac_059.mp3",
		"Van Helsingove poznámky! Musíte vykonať rituál presne podľa nich.": "res://audio/Kapitola_6/rozpravac_060.mp3",

		# Záverečné dialógy po druhom puzzle - Isabelle (2 dialógy)
		"Nie! Čo robíte? Ten rituál – to je nemožné!": "res://audio/Kapitola_6/Isabelle_007.mp3",
		"Nik nepozná staré spôsoby!": "res://audio/Kapitola_6/Isabelle_008.mp3",

		# Záverečné dialógy po druhom puzzle - rozprávač (5 dialógov s audiom, 1 bez audia)
		"Kruh zo svätenej vody zažiari. Sviece tvoria geometrický znak moci.": "res://audio/Kapitola_6/rozpravac_061.mp3",
		"Isabelle reve, no jej hlas už znie porazene.": "res://audio/Kapitola_6/rozpravac_062.mp3",
		"Strieborný kríž padne do stredu kruhu; ozve sa zvuk, akoby sa trhal závoj medzi svetmi.": "res://audio/Kapitola_6/rozpravac_063.mp3",
		"Isabelle sa rozpadá na prach, ktorý víri a mizne v svetle rituálu.": "res://audio/Kapitola_6/rozpravac_064.mp3",
		"Posledný výkrik stíchne. Po troch storočiach je definitívne mŕtva.": "res://audio/Kapitola_6/rozpravac_065.mp3"
		# "Hra je dokončená! Gratulujeme k víťazstvu nad temnotou!" - bez audia
	}
	print("✅ Kapitola 6 audio mapa inicializovaná s ", chapter6_audio_map.size(), " záznamami")

func initialize_chapter7_audio_mapping():
	# Mapovanie textov pre kapitolu 7 - rozprávač, Van Helsing a Viktor
	print("🎵 Inicializujem audio mapovanie pre kapitolu 7...")
	chapter7_audio_map = {
		# Úvodné dialógy rozprávača (3 dialógy)
		"Keď Isabelle zmizne, jedna zo stien sa pomaly odsunie.": "res://audio/Kapitola_7/rozpravac_066.mp3",
		"Za ňou je úzka komora. Na zemi, spútaný reťazami, no živý, leží doktor Van Helsing.": "res://audio/Kapitola_7/rozpravac_067.mp3",
		"Je bledý a slabý, no jeho oči sa rozjasnia, keď vás zazrie.": "res://audio/Kapitola_7/rozpravac_068.mp3",

		# Van Helsing úvodné dialógy (8 dialógov)
		"Vedel som... vedel som, že prídete.": "res://audio/Kapitola_7/Van_Helsing_001.mp3",
		"Moji najlepší žiaci by ma nenechali v pazúroch monštra.": "res://audio/Kapitola_7/Van_Helsing_002.mp3",
		"Isabelle ma uhryzla hneď prvú noc. Jed koluje v mojich žilách a premená ma...": "res://audio/Kapitola_7/Van_Helsing_003.mp3",
		"Cítim to. Každou hodinou som bližšie k tomu, čím bola ona.": "res://audio/Kapitola_7/Van_Helsing_004.mp3",
		"Váš elixír – spomalil proces. Získal mi čas.": "res://audio/Kapitola_7/Van_Helsing_005.mp3",
		"Ďakujem. Jed sa spomaľuje, no na úplné vyliečenie potrebujem viac.": "res://audio/Kapitola_7/Van_Helsing_006.mp3",
		"Musíme do môjho laboratória v Budapešti. Tam dokončíme rituál očistenia.": "res://audio/Kapitola_7/Van_Helsing_007.mp3",
		"Zachránili ste mňa, aj Európu. Keby sa Isabelle dostala na slobodu...": "res://audio/Kapitola_7/Van_Helsing_008.mp3",

		# Rozprávač medzi Van Helsing dialógmi (1 dialóg)
		"Podávate mu fľaštičku s elixírom. Van Helsing ju vypije jedným dúškom.": "res://audio/Kapitola_7/rozpravac_069.mp3",

		# Van Helsing pokračovanie (1 dialóg)
		"Ale je po všetkom. Teraz sa musíme sústrediť na moje uzdravenie.": "res://audio/Kapitola_7/Van_Helsing_009.mp3",

		# Záverečné dialógy (get_final_dialogue) - rozprávač (2 dialógy)
		"Stúpate späť chodbami zámku. Viktor vás čaká s úľavou v očiach.": "res://audio/Kapitola_7/rozpravac_070.mp3",
		"Keď vidí Van Helsinga živého, takmer sa rozplače.": "res://audio/Kapitola_7/rozpravac_071.mp3",

		# Viktor záverečné dialógy (3 dialógy)
		"Pán doktor! Vďaka Bohu, že žijete!": "res://audio/Kapitola_7/Viktor_34.mp3",
		"Už som sa obával najhoršieho.": "res://audio/Kapitola_7/Viktor_35.mp3",
		"Okamžite pripravím kočiar na cestu do Budapešti!": "res://audio/Kapitola_7/Viktor_36.mp3",

		# Van Helsing záverečné dialógy (3 dialógy)
		"Áno, Viktor. Musíme vyraziť čo najskôr.": "res://audio/Kapitola_7/Van_Helsing_010.mp3",
		"Času je dosť, ale nesmieme ho premárniť.": "res://audio/Kapitola_7/Van_Helsing_011.mp3",
		"A vy, moji priatelia... dokázali ste to.": "res://audio/Kapitola_7/Van_Helsing_012.mp3",

		# Rozprávač záverečné dialógy (6 dialógov)
		"Opúšťate zámok práve keď prvé lúče slnka prenikajú cez mraky.": "res://audio/Kapitola_7/rozpravac_072.mp3",
		"Búrka sa skončila. Vzduch je čistý a svieži.": "res://audio/Kapitola_7/rozpravac_073.mp3",
		"Za vami zostáva zámok, ktorý už nikdy nebude domovom zla.": "res://audio/Kapitola_7/rozpravac_074.mp3",
		"Pred vami je cesta do Budapešti a nádej na úplné uzdravenie vášho mentora.": "res://audio/Kapitola_7/rozpravac_075.mp3",
		"Prekliate dedičstvo rodu Báthoryovcov je definitívne zlomené.": "res://audio/Kapitola_7/rozpravac_076.mp3",
		"Váš príbeh sa končí, ale legenda o vašom hrdinskom čine bude žiť večne.": "res://audio/Kapitola_7/rozpravac_077.mp3"
	}
	print("✅ Kapitola 7 audio mapa inicializovaná s ", chapter7_audio_map.size(), " záznamami")

func set_current_chapter(chapter_number: int):
	current_chapter = chapter_number

func start_dialogue(dialogue_data: Array[Dictionary]):
	current_dialogue = dialogue_data
	current_line_index = 0
	# Uložiť stav dialógu
	GameManager.set_game_state_dialogue(dialogue_data, 0)
	show()
	display_current_line()

func display_current_line():
	if current_line_index >= current_dialogue.size():
		end_dialogue()
		return

	var line_data = current_dialogue[current_line_index]
	var speaker = line_data.get("speaker", "Rozprávač")
	var text = line_data.get("text", "")

	# Kontrola pre zmenu pozadia na konkrétnu vetu
	if text == "Vstupujete do rozľahlej knižnice. Regály siahajú až k stropu.":
		background_change_requested.emit("res://assets/pozadia/Kapitola_3/3.png")
	elif text == "Laboratórium vyzerá ako miesto, kde sa zázraky miešajú s hrôzami.":
		background_change_requested.emit("res://assets/pozadia/Kapitola_4/1.png")
	elif text == "Schádzate do oválnej sály s klenbou pokrytou prastarými symbolmi.":
		background_change_requested.emit("res://assets/pozadia/Kapitola_5/3.png")
	elif text == "Isabelle vystúpi zo sarkofágu. Už nie je tou kráskou z kroník.":
		background_change_requested.emit("res://assets/pozadia/Kapitola_6/3.png")

	# Prehrávanie audio pre rozprávača, kočiša, Viktora a Van Helsing záznamy
	print("🎵 Audio check - speaker: ", speaker, ", current_chapter: ", current_chapter)
	if (speaker == "Rozprávač" or speaker == "Kočiš") and current_chapter == 1:
		print("🎵 Spúšťam audio pre kapitolu 1")
		play_narrator_audio(text)
	elif (speaker == "Rozprávač" or speaker == "Viktor") and current_chapter == 2:
		print("🎵 Spúšťam audio pre kapitolu 2")
		play_narrator_audio(text)
	elif (speaker == "Rozprávač" or speaker == "Viktor") and current_chapter == 3:
		print("🎵 Spúšťam audio pre kapitolu 3")
		play_narrator_audio(text)
	elif (speaker == "Rozprávač" or speaker == "Viktor") and current_chapter == 4:
		print("🎵 Spúšťam audio pre kapitolu 4")
		play_narrator_audio(text)
	elif (speaker == "Rozprávač" or speaker == "Viktor" or speaker == "Van Helsing") and current_chapter == 5:
		print("🎵 Spúšťam audio pre kapitolu 5")
		play_narrator_audio(text)
	elif (speaker == "Rozprávač" or speaker == "Isabelle") and current_chapter == 6:
		print("🎵 Spúšťam audio pre kapitolu 6")
		play_narrator_audio(text)
	elif (speaker == "Rozprávač" or speaker == "Van Helsing" or speaker == "Viktor") and current_chapter == 7:
		print("🎵 Spúšťam audio pre kapitolu 7")
		play_narrator_audio(text)
	else:
		print("🎵 Audio sa nespúšťa - nesprávna kapitola alebo speaker")

	if speaker_label and is_instance_valid(speaker_label):
		speaker_label.text = speaker

	# Zobrazenie portrétu postavy
	show_character_portrait(speaker)

	# Kontrola či treba zmeniť hudbu na základe dialógu
	check_dialogue_music_trigger(text)

	# Kontrola atmosférických zvukových triggerov
	check_atmosphere_trigger(text, speaker)

	# Aplikovanie správneho fontu podľa typu postavy
	apply_character_font(speaker)

	# Kontrola pre blesk efekt v kapitole 1
	if current_chapter == 1 and text.to_lower().contains("blesk"):
		print("⚡ Detekované slovo 'blesk' v texte: ", text.substr(0, 50), "...")
		play_lightning_effect()

	# Animácia písania textu
	if text_label and is_instance_valid(text_label):
		text_label.text = ""
	is_typing = true
	if continue_button and is_instance_valid(continue_button):
		continue_button.disabled = true

	var full_text = line_data.get("text", "")
	type_text(full_text)

func type_text(text: String):
	if not text_label or not is_instance_valid(text_label):
		print("⚠️ text_label nie je dostupný v type_text()")
		return

	text_label.text = ""

	# Rozdelenie textu na slová
	current_text_words = text.split(" ")
	current_word_index = 0

	# Horizontálne odvíjanie po slovách
	for i in range(current_text_words.size()):
		if not is_typing:
			break

		current_word_index = i
		var displayed_text = ""

		# Zobraziť slová od aktuálneho slova
		for j in range(i + 1):
			if j > 0:
				displayed_text += " "
			displayed_text += current_text_words[j]

		if text_label and is_instance_valid(text_label):
			text_label.text = displayed_text

		await get_tree().create_timer(typing_speed).timeout

	is_typing = false
	if continue_button and is_instance_valid(continue_button):
		continue_button.disabled = false
		continue_button.grab_focus()

func _on_continue_pressed():
	if is_typing:
		# Preskočiť animáciu písania
		is_typing = false
		if text_label and is_instance_valid(text_label) and current_line_index < current_dialogue.size():
			text_label.text = current_dialogue[current_line_index].get("text", "")
		if continue_button and is_instance_valid(continue_button):
			continue_button.disabled = false
		return

	current_line_index += 1
	dialogue_advanced.emit()

	# Uložiť progress po každom dialógu
	if current_chapter > 0:
		GameManager.update_story_progress(current_chapter, current_line_index)

	display_current_line()

func play_narrator_audio(text: String):
	# Prehrá audio pre rozprávača, kočiša, Viktora, Van Helsing záznamy a Isabelle
	print("🎵 play_narrator_audio volané s textom: ", text.substr(0, 50), "...")
	print("🎵 Aktuálna kapitola: ", current_chapter)
	var audio_path = ""

	# Kontrola audio máp pre rôzne kapitoly
	if chapter1_narrator_audio_map.has(text):
		audio_path = chapter1_narrator_audio_map[text]
		print("🎵 Našiel som audio v chapter1_map: ", audio_path)
	elif chapter2_audio_map.has(text):
		audio_path = chapter2_audio_map[text]
		print("🎵 Našiel som audio v chapter2_map: ", audio_path)
	elif chapter3_audio_map.has(text):
		audio_path = chapter3_audio_map[text]
		print("🎵 Našiel som audio v chapter3_map: ", audio_path)
	elif chapter4_audio_map.has(text):
		audio_path = chapter4_audio_map[text]
		print("🎵 Našiel som audio v chapter4_map: ", audio_path)
	elif chapter5_audio_map.has(text):
		audio_path = chapter5_audio_map[text]
		print("🎵 Našiel som audio v chapter5_map: ", audio_path)
	elif chapter6_audio_map.has(text):
		audio_path = chapter6_audio_map[text]
		print("🎵 Našiel som audio v chapter6_map: ", audio_path)
	elif chapter7_audio_map.has(text):
		audio_path = chapter7_audio_map[text]
		print("🎵 Našiel som audio v chapter7_map: ", audio_path)
	else:
		print("🎵 Text nenájdený v žiadnej audio mape")
		print("🎵 Hľadaný text: '", text, "'")
		if current_chapter == 4:
			print("🎵 Dostupné kľúče v chapter4_audio_map:")
			for key in chapter4_audio_map.keys():
				print("   - '", key, "'")
				if key == text:
					print("     ✅ ZHODA!")
				elif key.similarity(text) > 0.8:
					print("     ⚠️ PODOBNÉ (", key.similarity(text), ")")
		elif current_chapter == 6:
			print("🎵 Dostupné kľúče v chapter6_audio_map:")
			for key in chapter6_audio_map.keys():
				print("   - '", key, "'")
				if key == text:
					print("     ✅ ZHODA!")
				elif key.similarity(text) > 0.8:
					print("     ⚠️ PODOBNÉ (", key.similarity(text), ")")
		elif current_chapter == 7:
			print("🎵 Dostupné kľúče v chapter7_audio_map:")
			for key in chapter7_audio_map.keys():
				print("   - '", key, "'")
				if key == text:
					print("     ✅ ZHODA!")
				elif key.similarity(text) > 0.8:
					print("     ⚠️ PODOBNÉ (", key.similarity(text), ")")

	if audio_path != "" and narrator_audio_player:
		print("🎵 Pokúšam sa načítať audio: ", audio_path)
		var audio_stream = load(audio_path)
		if audio_stream:
			narrator_audio_player.stream = audio_stream
			narrator_audio_player.play()
			print("✅ Prehrávam audio: ", audio_path)
		else:
			print("❌ CHYBA: Nemožno načítať audio súbor: ", audio_path)
	else:
		print("❌ Audio pre text nenájdené alebo narrator_audio_player chýba")
		print("   - audio_path: ", audio_path)
		print("   - narrator_audio_player: ", narrator_audio_player)

func play_lightning_effect():
	"""Prehrá realistický blesk efekt pomocou vylepšenej animácie a zvuku"""
	if lightning_video and current_chapter == 1:
		print("⚡ Spúšťam vylepšený blesk efekt")

		# Prehrá zvukový efekt blesku
		if AudioManager:
			AudioManager.play_sfx("res://audio/effects/Blesk.wav")
			print("⚡ Blesk zvukový efekt spustený")
		else:
			print("❌ AudioManager nedostupný pre blesk efekt")

		# Používame vylepšenú flash animáciu - vyzerá lepšie ako video
		play_enhanced_lightning_animation()
	else:
		print("❌ Lightning video nedostupný alebo nie je kapitola 1")

func play_enhanced_lightning_animation():
	"""Vylepšená realistická animácia blesku"""
	print("⚡ Spúšťam vylepšenú blesk animáciu")

	# Zobrazíme VideoStreamPlayer ako biely overlay
	lightning_video.visible = true
	lightning_video.modulate = Color(0.9, 0.95, 1.0, 0)  # Mierne modrastá biela pre realistickejší efekt

	# Realistická sekvencia blesku - viacero rýchlych flashov s rôznou intenzitou
	var tween = create_tween()

	# Prvý veľký flash - hlavný blesk
	tween.tween_property(lightning_video, "modulate:a", 0.85, 0.02)  # Veľmi rýchly nárast
	tween.tween_property(lightning_video, "modulate:a", 0.3, 0.05)   # Rýchly pokles
	tween.tween_property(lightning_video, "modulate:a", 0.0, 0.08)   # Úplné zmiznutie

	# Krátka pauza
	tween.tween_interval(0.12)

	# Druhý flash - ozvena blesku
	tween.tween_property(lightning_video, "modulate:a", 0.6, 0.015)  # Ešte rýchlejší
	tween.tween_property(lightning_video, "modulate:a", 0.0, 0.06)   # Rýchle zmiznutie

	# Dlhšia pauza
	tween.tween_interval(0.2)

	# Tretí flash - slabý dozvuk
	tween.tween_property(lightning_video, "modulate:a", 0.4, 0.01)   # Najrýchlejší
	tween.tween_property(lightning_video, "modulate:a", 0.0, 0.12)   # Pomalé zmiznutie

	tween.finished.connect(_on_lightning_finished)
	print("✅ Vylepšená blesk animácia spustená")

func _on_lightning_finished():
	"""Callback po dokončení blesk efektu"""
	print("⚡ Blesk efekt dokončený")
	if lightning_video:
		lightning_video.visible = false
		lightning_video.modulate = Color.WHITE  # Reset modulate

func end_dialogue():
	# Zastaviť audio ak hrá
	if narrator_audio_player and narrator_audio_player.playing:
		narrator_audio_player.stop()
	# Skryť portrét postavy
	show_character_portrait("")
	hide()
	print("🎬 DialogueSystem: Emitujem dialogue_finished signál")
	dialogue_finished.emit()

func _input(event):
	if visible and event.is_action_pressed("ui_accept"):
		_on_continue_pressed()
	elif visible and event.is_action_pressed("ui_cancel"):
		skip_dialogue()

func skip_dialogue():
	is_typing = false
	end_dialogue()

# Prednastavené dialógy pre rozprávača
# Funkcie pre získanie dialógov podľa fázy príbehu
func get_chapter_intro_dialogue(chapter_number: int) -> Array[Dictionary]:
	if chapter_number == 1:
		return [
			{"speaker": "Rozprávač", "text": "Marec 1894. Studený dážď bičuje okná kočiara kolísajúceho sa na úzkej ceste cez karpatské horstvo."},
			{"speaker": "Rozprávač", "text": "Búrka silnie s každou míľou, ktorou sa približujete k zámku Van Helsinga."},
			{"speaker": "Rozprávač", "text": "V kočiari sedíte už štyri hodiny, študujúc posledné poznámky svojho mentora."},
			{"speaker": "Rozprávač", "text": "Vzduch je napätý – nielen kvôli blížiacej sa búrke, ale aj preto, že cítite hrozbu niečoho omnoho nebezpečnejšieho."},
			{"speaker": "Rozprávač", "text": "Van Helsingov posledný telegram bol nezvyčajne stručný a naliehavý. To nie je jeho štýl – určite sa deje niečo vážne."},
			{"speaker": "Rozprávač", "text": "Vonku zúri živelný chaos: vietor vyje v korunách stromov ako tisíc duší v mukách."},
			{"speaker": "Rozprávač", "text": "Každý blesk na okamih osvetlí zahmlenú krajinu, akoby samotná príroda odmietala odhaliť svoje tajomstvá."},
			{"speaker": "Rozprávač", "text": "Kočiš je nervózny – už hodinu neprehovoril ani slovo."},
			{"speaker": "Rozprávač", "text": "Van Helsingov list obsahuje zašifrovanú správu. Jeho poznámka znie: 'Pamätaj - každé písmeno posuniem o jedno miesto dopredu.'"}
		]
	elif chapter_number == 2:
		return [
			{"speaker": "Rozprávač", "text": "Stojíte pred masívnou železnou bránou zdobenou heraldickými symbolmi."},
			{"speaker": "Rozprávač", "text": "Každý erb rozpráva príbeh – moc, pád dynastie, vzostup i úpadok."},
			{"speaker": "Rozprávač", "text": "Brána je zamknutá, v oknách nevidno svetlo. Zámok pôsobí opustene."},
			{"speaker": "Rozprávač", "text": "Havrany krákajú zo strešných ríms, akoby sa vysmievali vašej situácii."},
			{"speaker": "Rozprávač", "text": "Na bráne je krvou napísaný text: 'MOJE MENO JE SMRŤ, ALE POZADU SOM ŽIVOT'"}
		]
	elif chapter_number == 3:
		return [
			{"speaker": "Rozprávač", "text": "Vstupujete do veľkej haly osvetlenej len plamenným mihotaním krbu."},
			{"speaker": "Rozprávač", "text": "Steny zdobí zbierka starodávnych zbraní – meče, kopije, kuše."},
			{"speaker": "Rozprávač", "text": "V rohu tikajú staré hodiny; ich monotónne klikanie je jediné, čo narúša ticho."},
			{"speaker": "Viktor", "text": "Pán doktor odišiel včera večer krátko po západe slnka."},
			{"speaker": "Viktor", "text": "Povedal iba: 'Viktor, idem preskúmať staré krídlo zámku. Ak sa do úsvitu nevrátim, pošli telegram Rádu.'"},
			{"speaker": "Viktor", "text": "Odvtedy o ňom niet správy."},
			{"speaker": "Viktor", "text": "Do toho krídla nik nevošiel celé desaťročia. Je to najstaršia časť zámku."}
		]
	elif chapter_number == 4:
		return [
			{"speaker": "Rozprávač", "text": "Staré krídlo pôsobí ako vstup do iného sveta. Vzduch je ťažší, studenší."},
			{"speaker": "Rozprávač", "text": "Steny pokrýva vlhkosť a medzi kameňmi rastú čudné plesne."},
			{"speaker": "Rozprávač", "text": "Občas počuť mechanické cvakanie – v múroch stále pracujú skryté mechanizmy."},
			{"speaker": "Viktor", "text": "Počkajte! Spomínam si na básničku."},
			{"speaker": "Viktor", "text": "'Kráčaj, kde Mesiac svieti, nie tam, kde Slnko horí.' V núdzi ju vraj použijem."}
		]
	elif chapter_number == 5:
		return [
			{"speaker": "Rozprávač", "text": "Za laboratóriom sa skrýva úzke kamenné schodisko vedúce do hlbín."},
			{"speaker": "Rozprávač", "text": "Kamene sú ošúchané tisíckami krokov, no roky tu nik nebol."},
			{"speaker": "Rozprávač", "text": "Z hĺbky sála chlad a vlhkosť, pripomínajúce dych hrobky."},
			{"speaker": "Viktor", "text": "Počkám tu a budem strážiť ústup!"},
			{"speaker": "Viktor", "text": "Ak sa niečo stane, kričte. A vezmite si toto..."},
			{"speaker": "Viktor", "text": "Kríž požehnal sám ostrihomský arcibiskup. Môže vám zachrániť život."}
		]
	elif chapter_number == 6:
		return [
			{"speaker": "Rozprávač", "text": "Keď sa posledná pečať uvoľní, celý sarkofág sa otrasie."},
			{"speaker": "Rozprávač", "text": "Kameň praská pod tlakom ohromnej sily zvnútra."},
			{"speaker": "Rozprávač", "text": "Vzduch naplní pach smrti. Z trhlín sa valí biela para."},
			{"speaker": "Isabelle", "text": "Konečne... po troch storočiach je moja väznica otvorená."},
			{"speaker": "Isabelle", "text": "Starý hlupák Van Helsing poslúžil ako dokonalá návnada."},
			{"speaker": "Isabelle", "text": "A vy ste jeho dielo dokonali lepšie, než som dúfala."},
			{"speaker": "Isabelle", "text": "Ste z Rádu Striebornej ruže, však? Vidím vo vašich očiach strach i odhodlanie."}
		]
	elif chapter_number == 7:
		return [
			{"speaker": "Rozprávač", "text": "Keď Isabelle zmizne, jedna zo stien sa pomaly odsunie."},
			{"speaker": "Rozprávač", "text": "Za ňou je úzka komora. Na zemi, spútaný reťazami, no živý, leží doktor Van Helsing."},
			{"speaker": "Rozprávač", "text": "Je bledý a slabý, no jeho oči sa rozjasnia, keď vás zazrie."},
			{"speaker": "Van Helsing", "text": "Vedel som... vedel som, že prídete."},
			{"speaker": "Van Helsing", "text": "Moji najlepší žiaci by ma nenechali v pazúroch monštra."},
			{"speaker": "Van Helsing", "text": "Isabelle ma uhryzla hneď prvú noc. Jed koluje v mojich žilách a premená ma..."},
			{"speaker": "Van Helsing", "text": "Cítim to. Každou hodinou som bližšie k tomu, čím bola ona."},
			{"speaker": "Van Helsing", "text": "Váš elixír – spomalil proces. Získal mi čas."},
			{"speaker": "Rozprávač", "text": "Podávate mu fľaštičku s elixírom. Van Helsing ju vypije jedným dúškom."},
			{"speaker": "Van Helsing", "text": "Ďakujem. Jed sa spomaľuje, no na úplné vyliečenie potrebujem viac."},
			{"speaker": "Van Helsing", "text": "Musíme do môjho laboratória v Budapešti. Tam dokončíme rituál očistenia."},
			{"speaker": "Van Helsing", "text": "Zachránili ste mňa, aj Európu. Keby sa Isabelle dostala na slobodu..."},
			{"speaker": "Van Helsing", "text": "Ale je po všetkom. Teraz sa musíme sústrediť na moje uzdravenie."}
		]

	return [{"speaker": "Rozprávač", "text": "Kapitola sa načítava..."}]

# Dialógy medzi hlavolamami
func get_interlude_dialogue(chapter_number: int, phase: int) -> Array[Dictionary]:
	if chapter_number == 1:
		if phase == 1:  # Po prvom hlavolame
			return [
				{"speaker": "Rozprávač", "text": "Srdce vám takmer zastane: 'Grófka je v krypte.'"},
				{"speaker": "Rozprávač", "text": "Znamená to, že Van Helsing našiel, čo hľadal – Isabelle Báthoryovú, poslednú z prekliatej línie."},
				{"speaker": "Rozprávač", "text": "Prečo však nepríde za vami? Prečo vás volá na zámok? A prečo je jeho list taký ustráchaný?"},
				{"speaker": "Rozprávač", "text": "Blesk, prudké zastavenie kočiara."},
				{"speaker": "Kočiš", "text": "To je všetko! Ďalej nejdem, páni!"},
				{"speaker": "Kočiš", "text": "Ani korunu by ste mi nedali, aby som šiel bližšie k tomu prekliatemu miestu!"},
				{"speaker": "Kočiš", "text": "Hovorí sa, že kto sa priblíži k zámku po západe slnka, už nikdy neuvidí úsvit."},
				{"speaker": "Kočiš", "text": "Musíte pokračovať pešo. Nech vás Boh ochraňuje..."},
				{"speaker": "Rozprávač", "text": "Kočiš vám hodí batožinu do blata a bez slova odcvála."},
				{"speaker": "Rozprávač", "text": "Jeho kočiar mizne v hukote búrky. Ostávate sami – uprostred karpatskej divočiny, s nocou, ktorá sa rýchlo blíži."},
				{"speaker": "Rozprávač", "text": "Vaše kroky sa zabárajú do hlbokého blata."},
				{"speaker": "Rozprávač", "text": "Les pôsobí ako živá bytosť – počuť šepot medzi konármi, praskot vetvičiek, vzdialené vytie, ktoré neznie celkom vlčie."},
				{"speaker": "Rozprávač", "text": "Hmla klesá a každý strom pripomína strážcu sledujúceho vaše pohyby."},
				{"speaker": "Rozprávač", "text": "Cesta sa rozdeľuje na štyri smery. Van Helsingove poznámky obsahujú zvláštnu básničku."},
				{"speaker": "Rozprávač", "text": "V denníku je poznámka: 'Východ, potom dvakrát na západ, nakoniec sever - tak sa dostaneš k zámku.'"}
			]
		elif phase == 2:  # Po druhom hlavolame - záverečné dialógy
			return [
				{"speaker": "Rozprávač", "text": "Konečne! Cez koruny stromov sa črtajú obrysy mohutných veží."},
				{"speaker": "Rozprávač", "text": "Zámok Van Helsinga sa týči pred vami ako čierna silueta proti búrlivej oblohe."},
				{"speaker": "Rozprávač", "text": "Táto stavba z 13. storočia v dnešnú noc pripomína skôr hrobku dávno mŕtvych kráľov než domov živého človeka."}
			]
	elif chapter_number == 2:
		if phase == 1:  # Po prvom hlavolame
			return [
				{"speaker": "Rozprávač", "text": "Mechanizmus zaškrípe a brána sa pomaly otvára."},
				{"speaker": "Rozprávač", "text": "Vstupujete na nádvorie – dlažba je pokrytá machom a medzi kameňmi prerastá tráva."},
				{"speaker": "Rozprávač", "text": "Nádvorie je tiché ako hrob. Ticho prerušuje len kvapkanie vody zo žľabov."},
				{"speaker": "Rozprávač", "text": "Kdesi buchnú dvere – možno ich otvoril vietor, možno niečo iné."},
				{"speaker": "Viktor", "text": "Vy... ste to vy?"},
				{"speaker": "Viktor", "text": "Telegram odišiel pred troma dňami. Doktor vás čaká každú noc pri lampe."},
				{"speaker": "Viktor", "text": "Ale teraz nie je čas na spomienky."},
				{"speaker": "Viktor", "text": "Nie všetci, čo prichádzajú, patria medzi živých. Ak ste členovia Rádu, odpovedzte."},
				{"speaker": "Viktor", "text": "Tri otázky. Tri odpovede. Každá z iného sveta – kov, hviezda, bylina."}
			]
	elif chapter_number == 3:
		if phase == 1:  # Po prvom hlavolame
			return [
				{"speaker": "Viktor", "text": "Vonkajšie múry! Samozrejme!"},
				{"speaker": "Viktor", "text": "Tam, kde sa najstaršie základy zámku spájajú s novou stavbou."},
				{"speaker": "Viktor", "text": "Viem presne, kde to je - pri severozápadnej veži."},
				{"speaker": "Rozprávač", "text": "Vstupujete do rozľahlej knižnice. Regály siahajú až k stropu."},
				{"speaker": "Rozprávač", "text": "Knihy sú v rôznych jazykoch – latinčina, nemčina, rumunčina, maďarčina."},
				{"speaker": "Rozprávač", "text": "Väčšina diel sa venuje tematikám, ktoré nie sú pre slabé povahy: démonológia, vampirológia, alchýmia."},
				{"speaker": "Rozprávač", "text": "Za tapisériou rodokmeňa odhaľujete tajnú priehradku."},
				{"speaker": "Rozprávač", "text": "Ukrytá je v nej drobná kožená knižka – osobný denník Van Helsinga."}
			]
	elif chapter_number == 4:
		if phase == 1:  # Po prvom hlavolame
			return [
				{"speaker": "Rozprávač", "text": "Úspech! Prešli ste bez spustenia mechanizmov."},
				{"speaker": "Rozprávač", "text": "Na konci chodby stoja masívne dubové dvere."},
				{"speaker": "Rozprávač", "text": "Laboratórium vyzerá ako miesto, kde sa zázraky miešajú s hrôzami."},
				{"speaker": "Rozprávač", "text": "Stoly sú obsypané sklenenými nádobami, kotlíkmi a čudesnými prístrojmi."},
				{"speaker": "Viktor", "text": "Toto je recept na ochranný elixír!"},
				{"speaker": "Viktor", "text": "Doktor mi ho ukázal pred mesiacom."},
				{"speaker": "Viktor", "text": "Povedal: 'Viktor, ak ma pôjdeš hľadať do katakomb, priprav si tento elixír.'"}
			]
	elif chapter_number == 5:
		if phase == 1:  # Po prvom hlavolame
			return [
				{"speaker": "Rozprávač", "text": "Schádzate do oválnej sály s klenbou pokrytou prastarými symbolmi."},
				{"speaker": "Rozprávač", "text": "V strede stojí kamenný podstavec. Rozhadzané ležia doktorove veci."},
				{"speaker": "Rozprávač", "text": "Rozbitá lampa, prázdny strieborný revolver, kožený notes s roztrhanými stranami."},
				{"speaker": "Rozprávač", "text": "Na kameňoch tmavnú škvrny, čo nápadne pripomínajú krv. No doktora niet."},
				{"speaker": "Van Helsing", "text": "Je tu! Isabelle má prisluhovačov – nie upírov, ale niečo horšie."},
				{"speaker": "Van Helsing", "text": "Ako vedela, že prídem? Musím sa dostať k jej sarkofágu..."},
				{"speaker": "Van Helsing", "text": "Voda dochádza, striebro tiež... sú ich príliš veľa..."},
				{"speaker": "Van Helsing", "text": "Ak toto niekto nájde, dokončite, čo som začal..."},
				{"speaker": "Rozprávač", "text": "Za podstavcom odkrývate tajné dvere vedúce do rozľahlej sály."},
				{"speaker": "Rozprávač", "text": "Uprostred stojí mohutný sarkofág zdobený tromi pákami."}
			]
	elif chapter_number == 6:
		if phase == 1:  # Po prvom hlavolame
			return [
				{"speaker": "Isabelle", "text": "Bystré! No múdrosť vás neochráni pred mojou silou."},
				{"speaker": "Isabelle", "text": "Tri storočia som sa živila snami o pomste."},
				{"speaker": "Rozprávač", "text": "Isabelle vystúpi zo sarkofágu. Už nie je tou kráskou z kroník."},
				{"speaker": "Rozprávač", "text": "Stáročia magického spánku ju premenili na niečo horšie než smrť."},
				{"speaker": "Rozprávač", "text": "Van Helsingove poznámky! Musíte vykonať rituál presne podľa nich."}
			]

	return []

func get_final_dialogue(chapter_number: int) -> Array[Dictionary]:
	if chapter_number == 7:
		return [
			{"speaker": "Rozprávač", "text": "Stúpate späť chodbami zámku. Viktor vás čaká s úľavou v očiach."},
			{"speaker": "Rozprávač", "text": "Keď vidí Van Helsinga živého, takmer sa rozplače."},
			{"speaker": "Viktor", "text": "Pán doktor! Vďaka Bohu, že žijete!"},
			{"speaker": "Viktor", "text": "Už som sa obával najhoršieho."},
			{"speaker": "Viktor", "text": "Okamžite pripravím kočiar na cestu do Budapešti!"},
			{"speaker": "Van Helsing", "text": "Áno, Viktor. Musíme vyraziť čo najskôr."},
			{"speaker": "Van Helsing", "text": "Času je dosť, ale nesmieme ho premárniť."},
			{"speaker": "Van Helsing", "text": "A vy, moji priatelia... dokázali ste to."},
			{"speaker": "Rozprávač", "text": "Opúšťate zámok práve keď prvé lúče slnka prenikajú cez mraky."},
			{"speaker": "Rozprávač", "text": "Búrka sa skončila. Vzduch je čistý a svieži."},
			{"speaker": "Rozprávač", "text": "Za vami zostáva zámok, ktorý už nikdy nebude domovom zla."},
			{"speaker": "Rozprávač", "text": "Pred vami je cesta do Budapešti a nádej na úplné uzdravenie vášho mentora."},
			{"speaker": "Rozprávač", "text": "Prekliate dedičstvo rodu Báthoryovcov je definitívne zlomené."},
			{"speaker": "Rozprávač", "text": "Váš príbeh sa končí, ale legenda o vašom hrdinskom čine bude žiť večne."}
		]

	return []

func get_puzzle_intro_dialogue(chapter_number: int, puzzle_number: int) -> Array[Dictionary]:
	if chapter_number == 1:
		if puzzle_number == 1:
			return [
				{"speaker": "Rozprávač", "text": "Zašifrovaná správa: 'HSÔGLB KF X LSZQUF'"}
			]
		elif puzzle_number == 2:
			return [
				{"speaker": "Rozprávač", "text": "Musíte nájsť správnu cestu k zámku."}
			]
	elif chapter_number == 2:
		if puzzle_number == 1:
			return [
				{"speaker": "Rozprávač", "text": "Rozlúštite krvavý nápis na bráne."}
			]
		elif puzzle_number == 2:
			return [
				{"speaker": "Rozprávač", "text": "Dokážte, že patríte k Rádu."}
			]
	elif chapter_number == 3:
		if puzzle_number == 1:
			return [
				{"speaker": "Rozprávač", "text": "Na stole nájdete Van Helsingov denník. Posledná stránka obsahuje zvláštnu správu."}
			]
		elif puzzle_number == 2:
			return [
				{"speaker": "Rozprávač", "text": "V denníku je poznámka o Isabelle Báthoryovej."}
			]
	elif chapter_number == 4:
		if puzzle_number == 1:
			return [
				{"speaker": "Rozprávač", "text": "Na stene blikne krátka sekvencia 3 farieb. Musíte ju zopakovať."}
			]
		elif puzzle_number == 2:
			return [
				{"speaker": "Rozprávač", "text": "Na tabuli sú tri rovnice s vampírskymi symbolmi."}
			]
	elif chapter_number == 5:
		if puzzle_number == 1:
			return [
				{"speaker": "Rozprávač", "text": "V miestnosti sú 4 sviečky vrhajúce tiene s číslami: 7, 3, 9, 5."},
				{"speaker": "Rozprávač", "text": "Na podlahe je nápis: 'Súčet páru je tucet'"}
			]
		elif puzzle_number == 2:
			return [
				{"speaker": "Rozprávač", "text": "Na stene je vyškriabaný odkaz: 'Kráčaj, kde mesiac svieti, nie tam, kde slnko horí. Hviezda ti ukáže cestu.'"}
			]
	elif chapter_number == 6:
		if puzzle_number == 1:
			return [
				{"speaker": "Rozprávač", "text": "Na stene sú tri portréty sestier s menovkami: Mária, Anna, Isabelle."},
				{"speaker": "Rozprávač", "text": "Pod portrétmi je starý pergamen s tromi výrokmi."},
				{"speaker": "Van Helsing", "text": "Len jedna hovorí pravdu. Tá, čo klame, je vinná."}
			]
		elif puzzle_number == 2:
			return [
				{"speaker": "Rozprávač", "text": "Van Helsingov rituál napísaný krvou: 'Raz-raz-dva-dva-raz'"},
				{"speaker": "Rozprávač", "text": "Máte 4 symboly: kríž, voda, oheň, soľ"}
			]

	return [
		{"speaker": "Rozprávač", "text": "Pred vami sa objavuje nový hlavolam..."}
	]

func get_puzzle_success_dialogue(chapter_number: int = 0, puzzle_number: int = 0) -> Array[Dictionary]:
	if chapter_number == 1 and puzzle_number == 2:
		return [
			{"speaker": "Rozprávač", "text": "Konečne! Cez koruny stromov sa črtajú obrysy mohutných veží."},
			{"speaker": "Rozprávač", "text": "Zámok Van Helsinga sa týči pred vami ako čierna silueta proti búrlivej oblohe."},
			{"speaker": "Rozprávač", "text": "Táto stavba z 13. storočia v dnešnú noc pripomína skôr hrobku dávno mŕtvych kráľov než domov živého človeka."}
		]
	elif chapter_number == 2 and puzzle_number == 2:
		return [
			{"speaker": "Viktor", "text": "Tak predsa. Svetlo ešte úplne nezhaslo."},
			{"speaker": "Viktor", "text": "Poďte. A nech vás nočné tiene nespoznajú ako hostí."}
		]
	elif chapter_number == 3 and puzzle_number == 2:
		return [
			{"speaker": "Viktor", "text": "15. marca 1894 – Konečne!"},
			{"speaker": "Viktor", "text": "Po štyroch rokoch neúnavného pátrania som našiel dôkaz, že grófka Isabelle Báthoryová prežila svoju údajnú smrť."},
			{"speaker": "Viktor", "text": "Oficiálne záznamy sú prepracovaná lož."},
			{"speaker": "Viktor", "text": "Našiel som ju! Skrýva sa v kryptách pod týmto zámkom."},
			{"speaker": "Viktor", "text": "18. marca – Pripravujem sa na zostup do katakomb."},
			{"speaker": "Viktor", "text": "Potrebujem: strieborné gule, svätenú vodu z Ríma, dubový kôl, kríž môjho starého otca."},
			{"speaker": "Viktor", "text": "19. marca – Viktor netuší, kam idem. Tak je to lepšie."},
			{"speaker": "Viktor", "text": "Ak sa nevrátim do úsvitu, nech privolá Rád."},
			{"speaker": "Viktor", "text": "Krypty! Nik tam nevkročil celé storočia."},
			{"speaker": "Viktor", "text": "Vchod je zapečatený. Čo to pán doktor robil?"}
		]
	elif chapter_number == 4 and puzzle_number == 2:
		return [
			{"speaker": "Rozprávač", "text": "Elixír v fľaštičke jemne zažiari striebristým svetlom."},
			{"speaker": "Viktor", "text": "Výborne! Teraz sa môžeme odvážiť do katakomb."},
			{"speaker": "Viktor", "text": "Nezabudnite – elixír chráni len hodinu."}
		]
	# Kapitola 5 - po druhom puzzle rovno prechod na kapitolu 6 (bez dialógov)
	elif chapter_number == 6 and puzzle_number == 2:
		return [
			{"speaker": "Isabelle", "text": "Nie! Čo robíte? Ten rituál – to je nemožné!"},
			{"speaker": "Isabelle", "text": "Nik nepozná staré spôsoby!"},
			{"speaker": "Rozprávač", "text": "Kruh zo svätenej vody zažiari. Sviece tvoria geometrický znak moci."},
			{"speaker": "Rozprávač", "text": "Isabelle reve, no jej hlas už znie porazene."},
			{"speaker": "Rozprávač", "text": "Strieborný kríž padne do stredu kruhu; ozve sa zvuk, akoby sa trhal závoj medzi svetmi."},
			{"speaker": "Rozprávač", "text": "Isabelle sa rozpadá na prach, ktorý víri a mizne v svetle rituálu."},
			{"speaker": "Rozprávač", "text": "Posledný výkrik stíchne. Po troch storočiach je definitívne mŕtva."},
			{"speaker": "Rozprávač", "text": "Hra je dokončená! Gratulujeme k víťazstvu nad temnotou!"}
		]

	var success_messages = [
		[{"speaker": "Rozprávač", "text": "Výborne! Úspešne ste vyriešili hlavolam."}],
		[{"speaker": "Rozprávač", "text": "Bravó! Vaša logika vás neviedla."}],
		[{"speaker": "Rozprávač", "text": "Skvelé! Ďalšia záhada je odhalená."}]
	]
	return success_messages[randi() % success_messages.size()]

func get_puzzle_hint_dialogue(chapter_number: int = 0, puzzle_number: int = 0, hint_level: int = 1) -> Array[Dictionary]:
	if chapter_number == 1 and puzzle_number == 1:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Skúste posunúť každé písmeno o jedno miesto dozadu v abecede."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Napríklad H sa stane G, S sa stane R."}]
			3:
				return [{"speaker": "Rozprávač", "text": "Prvé slovo je GRÓFKA. Pokračujte ďalej."}]
	elif chapter_number == 1 and puzzle_number == 2:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Sledujte presne poradie v poznámke."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Prvý smer je východ (V)."}]
			3:
				return [{"speaker": "Rozprávač", "text": "Celé poradie: Východ, Západ, Západ, Sever."}]
	elif chapter_number == 2 and puzzle_number == 1:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Čítaj pozorne - niekedy je odpoveď v opaku."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Smrť pozadu... čo dostaneš, keď otočíš slovo?"}]
			3:
				return [{"speaker": "Rozprávač", "text": "SMRŤ odzadu je TRMS, ale nezabudni na piatu literu z 'SOM' → TRMSO"}]
	elif chapter_number == 2 and puzzle_number == 2:
		match hint_level:
			1:
				return [{"speaker": "Viktor", "text": "Tri svety - kov, hviezda, bylina. Každý má svoju moc proti temnote."}]
			2:
				return [{"speaker": "Viktor", "text": "Kov čistí duše, hviezda ovláda vody, bylina chráni hroby."}]
			3:
				return [{"speaker": "Viktor", "text": "Striebro, Mesiac, Cesnak - základy každého lovca upírov."}]
	elif chapter_number == 3 and puzzle_number == 1:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Van Helsing píše, že píše slová odzadu. Čo to znamená?"}]
			2:
				return [{"speaker": "Rozprávač", "text": "Skúste čítať každé slovo pozpätku."}]
			3:
				return [{"speaker": "Rozprávač", "text": "DOP = POD, IMÍŠJAKNOV = VONKAJŠÍMI..."}]
	elif chapter_number == 3 and puzzle_number == 2:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Je to jednoduchá matematika."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Od roku sobáša odpočítajte vek."}]
			3:
				return [{"speaker": "Rozprávač", "text": "1596 - 20 = ?"}]
	elif chapter_number == 4 and puzzle_number == 1:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Sústreďte sa a zapamätajte si poradie farieb."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Bola to sekvencia troch farieb."}]
			3:
				return [{"speaker": "Rozprávač", "text": "Skúste znova a dávajte lepší pozor."}]
	elif chapter_number == 4 and puzzle_number == 2:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Každý symbol skrýva číslo. Začnite s netopierom."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Ak dva netopiere dávajú 16, koľko je jeden netopier?"}]
			3:
				return [{"speaker": "Rozprávač", "text": "Netopier=8, Krv=11, Rakva=7. Spočítajte ich."}]
	elif chapter_number == 5 and puzzle_number == 1:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Tiene nikdy neklamú, ale musíš ich správne spárovať."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Tucet je 12. Ktoré dve čísla dávajú dokopy 12?"}]
			3:
				return [{"speaker": "Rozprávač", "text": "7+5=12 a 3+9=12. Zadaj všetky štyri čísla: 7539"}]
	elif chapter_number == 5 and puzzle_number == 2:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Odkaz si pamätáš? 'Kde Mesiac svieti...'"}]
			2:
				return [{"speaker": "Rozprávač", "text": "Slnko horí - tam nechoď. Mesiac svieti - tam choď."}]
			3:
				return [{"speaker": "Rozprávač", "text": "Postupuj podľa básničky: najprv Mesiac, potom Hviezda."}]
	elif chapter_number == 6 and puzzle_number == 1:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Ak len jedna hovorí pravdu, skúste postupne predpokladať, že pravdu hovorí každá z nich."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Overte každú možnosť: vedie k logickému sporu alebo nie?"}]
			3:
				return [{"speaker": "Rozprávač", "text": "Ak Mária hovorí pravdu, Anna je nevinná a Isabelle nezradila. Mária je správna odpoveď!"}]
	elif chapter_number == 6 and puzzle_number == 2:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "'Raz' a 'dva' označujú poradie symbolov."}]
			2:
				return [{"speaker": "Rozprávač", "text": "'Raz' = prvý symbol (kríž), 'dva' = druhý symbol (voda)."}]
			3:
				return [{"speaker": "Rozprávač", "text": "Stlačte: prvý, prvý, druhý, druhý, prvý."}]

	var hints = [
		[{"speaker": "Rozprávač", "text": "Možno by ste sa mali pozrieť na problém z iného uhla..."}],
		[{"speaker": "Rozprávač", "text": "Pamätajte, nie vždy je prvé riešenie to správne."}],
		[{"speaker": "Rozprávač", "text": "Skúste sa sústrediť na detaily, ktoré ste možno prehliadli."}]
	]
	return hints[randi() % hints.size()]

func apply_character_font(speaker: String):
	"""Aplikuje správny font podľa typu postavy"""
	if not text_label:
		return

	# Použitie FontLoader systému pre správne fonty
	if speaker == "Rozprávač":
		# Rozprávačský text - Cormorant italic, svetlá sivá
		FontLoader.apply_font_style(text_label, "narrator_text")
		print("🎨 Aplikovaný narrator font pre: ", speaker)
	else:
		# Dialógy postav - Cormorant regular, krémová
		FontLoader.apply_font_style(text_label, "character_dialogue")
		print("🎨 Aplikovaný character dialogue font pre: ", speaker)

func apply_dialogue_fonts():
	"""Aplikuje fonty a farby na dialogue elementy"""
	# Použitie FontLoader systému pre UI elementy
	if continue_label and is_instance_valid(continue_label):
		FontLoader.apply_font_style(continue_label, "ui_elements", 18)
		print("🎨 Aplikovaný UI font na continue_label")

	# Aplikovanie fontu na main menu label
	if main_menu_label and is_instance_valid(main_menu_label):
		FontLoader.apply_font_style(main_menu_label, "ui_elements", 18)
		print("🎨 Aplikovaný UI font na main_menu_label")

func _on_main_menu_pressed():
	"""Návrat do hlavného menu s uložením progresu"""
	print("Návrat do hlavného menu z dialógu")
	# Uložiť aktuálny stav dialógu
	GameManager.set_game_state_dialogue(current_dialogue, current_line_index)
	AudioManager.play_menu_button_sound()
	GameManager.go_to_main_menu()

func restore_dialogue_position(dialogue_data: Array, line_index: int):
	"""Obnoví dialóg na konkrétnej pozícii"""
	print("🔄 Obnovujem dialóg na pozícii: ", line_index, " z ", dialogue_data.size(), " riadkov")

	if dialogue_data.size() == 0:
		print("❌ Žiadne dialogue dáta na obnovenie")
		return

	# Nastaviť aktuálnu kapitolu pre správne audio
	current_chapter = GameManager.last_played_chapter
	print("🎵 Nastavujem current_chapter na: ", current_chapter)

	# Nastaviť dialogue dáta
	current_dialogue = dialogue_data
	current_line_index = line_index

	# Skontrolovať hranice
	if current_line_index >= current_dialogue.size():
		current_line_index = current_dialogue.size() - 1

	if current_line_index < 0:
		current_line_index = 0

	# Zobraziť dialogue panel
	show()
	visible = true

	# Zobraziť aktuálny riadok
	display_current_line()

	print("✅ Dialóg obnovený na pozícii: ", current_line_index)

func show_character_portrait(speaker: String):
	"""Zobrazí portrét postavy podľa mena speakera"""
	if not character_portrait or not is_instance_valid(character_portrait):
		print("⚠️ character_portrait nie je dostupný")
		return

	# Ak je speaker prázdny, skryť portrét
	if speaker == "":
		character_portrait.visible = false
		print("👤 Skrývam portrét postavy")
		return

	# Skontrolovať či máme portrét pre túto postavu
	if character_portraits.has(speaker):
		var portrait_path = character_portraits[speaker]
		var texture = load(portrait_path)
		if texture:
			character_portrait.texture = texture
			character_portrait.visible = true
			print("👤 Zobrazujem portrét pre: ", speaker)
		else:
			character_portrait.visible = false
			print("⚠️ Nemožno načítať portrét pre: ", speaker, " z cesty: ", portrait_path)
	else:
		# Postava nemá portrét - skryť
		character_portrait.visible = false
		print("👤 Žiadny portrét pre: ", speaker)

func check_dialogue_music_trigger(text: String):
	"""Kontroluje či dialóg má spustiť zmenu hudby"""
	if dialogue_music_triggers.has(text):
		var music_track = dialogue_music_triggers[text]
		print("🎵 Dialóg trigger: Spúšťam hudbu '", music_track, "'")
		AudioManager.play_music(music_track)
	else:
		# Kontrola čiastočných zhôd pre dlhšie texty
		for trigger_text in dialogue_music_triggers:
			if text.begins_with(trigger_text.substr(0, 30)):  # Prvých 30 znakov
				var music_track = dialogue_music_triggers[trigger_text]
				print("🎵 Čiastočná zhoda: Spúšťam hudbu '", music_track, "'")
				AudioManager.play_music(music_track)
				return

func check_atmosphere_trigger(text: String, speaker: String):
	"""Kontroluje atmosférické zvukové triggery pre kapitoly 1-3"""
	if not AudioManager:
		return

	# Kontrola len pre rozprávača v kapitolách 1-3
	if speaker != "Rozprávač":
		return

	# KAPITOLA 1 - STORM TRIGGERY
	if current_chapter == 1:
		# Kontrola špecifických textových triggerov
		if text.contains("Búrka sa zúri") or text.contains("kočiar sa trasie"):
			print("🌪️ Trigger: Spúšťam storm atmosféru")
			AudioManager.start_storm_atmosphere()
		elif text.contains("Rozprávač sa ozýva") or text.begins_with("V tejto temnej noci"):
			print("🌪️ Trigger: ROZPRÁVAČ_001 - znižujem storm")
			AudioManager.storm_narrator_start()
		elif text.contains("živelný chaos") or text.contains("blesky osvetľujú"):
			print("🌪️ Trigger: ROZPRÁVAČ_006 - chaos")
			AudioManager.storm_chaos()
		elif text.contains("vstupujete do lesa") or text.contains("stromy vás obklopujú"):
			print("🌪️ Trigger: ROZPRÁVAČ_015 - les")
			AudioManager.storm_forest()
		elif text.contains("pred bránou") or text.contains("zámok sa týči"):
			print("🌪️ Trigger: ROZPRÁVAČ_022 - fade out")
			AudioManager.storm_fade_out()

	# KAPITOLA 2 - BRÁNA TRIGGERY
	elif current_chapter == 2:
		if text.contains("pred bránou") or text.contains("opustený zámok"):
			print("🚪 Trigger: ROZPRÁVAČ_022 - spúšťam bránu")
			AudioManager.start_gate_atmosphere()
		elif text.contains("havrany krákajú") or text.contains("temné tiene"):
			print("🚪 Trigger: ROZPRÁVAČ_025 - havrany")
			AudioManager.gate_ravens()
		elif text.contains("brána sa otvára") or text.contains("ťažké dvere"):
			print("🚪 Trigger: ROZPRÁVAČ_026 - otváranie")
			AudioManager.gate_opening()
		elif text.contains("nádvorie") or text.contains("vstupujete do zámku"):
			print("🚪 Trigger: ROZPRÁVAČ_028 - nádvorie")
			AudioManager.gate_courtyard()

	# KAPITOLA 3 - VSTUPNÁ HALA TRIGGERY
	elif current_chapter == 3:
		if text.contains("vstupná hala") or text.contains("veľká hala"):
			print("🏰 Trigger: ROZPRÁVAČ_030 - vstupná hala")
			AudioManager.start_hall_atmosphere()
		elif text.contains("tikanie hodín") or text.contains("starožitné hodiny"):
			print("🏰 Trigger: ROZPRÁVAČ_032 - hodiny")
			AudioManager.hall_clock_emphasis()
		elif text.contains("knižnica") or text.contains("vstupujete do knižnice"):
			print("🏰 Trigger: ROZPRÁVAČ_033 - knižnica")
			AudioManager.hall_library_transition()
		elif text.contains("staré krídlo") or text.contains("opúšťate halu"):
			print("🏰 Trigger: ROZPRÁVAČ_038 - staré krídlo")
			AudioManager.hall_stop()
